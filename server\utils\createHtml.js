const { getmerge<PERSON>son, cloneAndGetCommits, readFile, getProjectInfo, formatTimestamp, getFileNames, clearCatalogue, writeJsonFile } = require('./tools');
const { traverseJson, getImageVersion, getProductionCommit, gitlabIp } = require('./simplegit'); // 导入 headers 和 gitlabIp
const axios = require('axios'); // 导入 axios 用于 API 调用
const { createCoverageMap } = require("../../istanbul-lib-coverage");
const { createContext } = require("../../istanbul-lib-report");
const { createSourceMapStore } = require("../../istanbul-lib-source-maps");
const { create: createReporter } = require('../../istanbul-reports');
const path = require('path');
const fs = require('fs');
let ip = process.env.APP_IP;

// 初始化，生成Html
const generateHtml = function ({ country, applicationName, isIncrement }) {
    return new Promise(async (resolve, reject) => {
        try {
            // 判断是否有任务
            console.log("----->generateHtml 判断是否有正在执行的任务")
            const { folders = [], maxTimestamp } = await getFileNames(`./json/${country}/${applicationName}`)
            if (!folders.length) {
                console.log("----->generateHtml 没有正在执行的任务")
                resolve({ code: 401, msg: '没有正在执行的任务', data: null });
                return;
            }
            // 0.获取git需要的参数
            console.log("----->generateHtml 获取git需要的参数")
            let projectData = await getProjectInfo({ country: country?.replace('jms', ''), applicationName });
            let { projectId = '', gitToken = '' } = projectData?.data || {}
            console.log("----->generateHtml 开始创建html")
            let resultHtml = await createHtml({ country, applicationName, appId: projectId, token: gitToken, isIncrement: isIncrement === 'true', maxTimestamp });
            console.log("----->generateHtml 创建html成功")
            resolve({ code: 1, msg: '请求成功', data: { ...resultHtml } });
        } catch (error) {
            resolve({ code: 401, msg: '没有正在执行的任务', data: null });
        }
    });
}

// 获取html报告
const getHtmlList = async function ({ country, applicationName, isIncrement }) {
    return new Promise(async (resolve, reject) => {
        try {
            // 全量、增量
            let subdirectorie = isIncrement === 'true' ? 'increment' : 'quantity'
            // 1. 创建目录
            const coverageDir = path.join(__dirname, `../../coverage/${country}/${applicationName}`);
            console.log("----->getHtmlList 获取文件名")
            const { maxTimestamp } = await getFileNames(`./json/${country}/${applicationName}`)
            // 2. 获取所有国家目录
            const countries = fs.readdirSync(coverageDir, { withFileTypes: true })
                .filter(dirent => dirent.isDirectory())
                .map(dirent => dirent.name);
            // 3. 获取国家下面所有项目目录
            const folders = await Promise.all(countries.map(async (dirent) => {
                try {
                    let pathName = `coverage/${country}/${applicationName}/${dirent}/${subdirectorie}`
                    console.log("----->getHtmlList 读取data.json")
                    let { readData = {} } = await readFile(`./${pathName}/data.json`);
                    const readDataJson = readData ? JSON.parse(readData) : null;
                    return {
                        taskId: dirent,
                        basePath: readDataJson ? `/${pathName}/html/index.html` : null,
                        path: readDataJson ? `${ip}/${pathName}/html/index.html` : null,
                        createTaskTime: formatTimestamp(Number(dirent)),
                        taskStatus: maxTimestamp === Number(dirent) ? 1 : 2,
                        ...(readDataJson || {})
                    };
                } catch (error) {
                    console.error(`Error processing ${dirent}:`, error);
                    return null;
                }
            }));
            const validFolders = folders.filter(Boolean);
            validFolders.sort((a, b) => b.taskId - a.taskId);
            resolve({ code: 1, msg: '请求成功', data: validFolders });
        } catch (error) {
            resolve({ code: 1, msg: '请求成功', data: [] });
        }

    });
}

// 生成html文件
const createHtml = function ({ country, applicationName, appId = 8118, projectName = 'sqs', token, isIncrement = false, maxTimestamp }) {
    return new Promise(async (resolve, reject) => {
        try {
            console.log("----->createHtml 获取覆盖率json文件")
            let { coverageJson } = await getmergeJson({ country, applicationName, maxTimestamp });
            // 1. 动态获取 GitLab 项目的 http_url_to_repo
            console.log("----->createHtml 获取 GitLab 项目的 http_url_to_repo")
            const projectDetails = await getGitLabProjectDetails(appId, token);
            if (!projectDetails || !projectDetails.http_url_to_repo) {
                console.error(`无法获取项目 ${appId} ${token} 的 Git URL。`);
                reject(new Error(`无法获取项目 ${appId} 的 Git URL。`));
                return;
            }
            const http_url_to_repo = projectDetails.http_url_to_repo;

            let authenticatedGitUrl;
            if (http_url_to_repo.startsWith("https://")) {
                authenticatedGitUrl = http_url_to_repo.replace("https://", `https://oauth2:${token}@`);
            } else if (http_url_to_repo.startsWith("http://")) {
                authenticatedGitUrl = http_url_to_repo.replace("http://", `http://oauth2:${token}@`);
            } else {
                console.error("无法识别的 Git URL 协议:", http_url_to_repo);
                reject(new Error(`无法识别的 Git URL 协议: ${http_url_to_repo}`));
                return;
            }

            // 2.下载代码到本地
            let setPath = `./project/${country}/${applicationName}`;
            console.log("----->createHtml 下载代码到本地")
            await cloneAndGetCommits(authenticatedGitUrl, setPath, "production");
            // 3.把json地址转换为相对路径
            console.log("----->createHtml getImageVersion")
            await getImageVersion({
                projectName,
                applicationName,
                productName: country
            })
            console.log("----->createHtml getProductionCommit")
            await getProductionCommit({ appId, token });
            console.log("----->createHtml traverseJson")
            let dealWithCoverageJson = await traverseJson({ coverageJson, isIncrement, token, appId });
            console.log("----->createHtml handlerData")
            const coverageData = await handlerData(JSON.parse(JSON.stringify(dealWithCoverageJson)), setPath);
            // 4.创建覆盖率地图
            const coverageMap = createCoverageMap(coverageData);
            // 获取并打印覆盖率汇总数据
            let coverageSummary = coverageMap.getCoverageSummary();
            // lines 表示代码行覆盖率
            // statements 表示语句覆盖率
            // functions 表示函数覆盖率
            // branches 表示分支覆盖率
            // if(coverageSummary){
            //     coverageSummary = JSON.parse(JSON.stringify(coverageSummary)?.replaceAll('Unknown',''))
            // }
            console.log("覆盖率汇总数据:",coverageSummary);
            // 5.转换覆盖率数据（处理源映射）
            const sourceMapStore = createSourceMapStore();
            const transformedCoverage = await sourceMapStore.transformCoverage(
                coverageMap
            );
            // 6.1创建存放目录
            // 全量、增量
            let subdirectorie = (isIncrement=== 'true'|| isIncrement=== true)  ? 'increment' : 'quantity'
            let setHtmlPath = `./coverage/${country}/${applicationName}/${maxTimestamp}/${subdirectorie}`
            console.log(setHtmlPath, isIncrement, "----->createHtml 创建存放目录")
            await clearCatalogue(setHtmlPath)
            // 6.2创建报告上下文
            const context = createContext({
                dir: setHtmlPath,
                coverageMap: transformedCoverage,
                sourceFinder: sourceMapStore.sourceFinder,
            });
            // 7.创建HTML报告
            const reporter = createReporter("html", {
                subdir: "html",
            });

            reporter.execute(context);
            // 8.创建一个JSON报告以便查看原始数据
            const jsonReporter = createReporter("json", {
                file: "coverage.json",
            });
            jsonReporter.execute(context);
            // 9.汇总详情数据保存
            console.log("----->createHtml 汇总详情数据保存")
            await writeJsonFile(`${setHtmlPath}/data.json`, { coverageSummary, filterCoverageJson: dealWithCoverageJson, generateTime: formatTimestamp(Number(new Date().getTime())) })
            console.log("----->createHtml 汇总详情数据保存成功")
            resolve({ data: true });
        } catch (error) {
            reject(error);
        }
    });
}

// 把json地址转换为相对路径
const handlerData = function (coverageJson, setPath) {
    return new Promise((resolve, reject) => {
        for (let key in coverageJson) {
            let oldPath = coverageJson[key].path;
            const parts = oldPath.split("src");
            let pUrl = `${setPath}/src${parts[1]}`;
            let newPath = pUrl.replace(/\\/g, '/');
            // 获取标准化 JSON
            coverageJson[key] = typeof coverageJson[key] !== 'object' ? coverageJson[key]?.toJSON() : coverageJson[key];
            coverageJson[key].path = newPath;
            if (key !== newPath) {
                coverageJson[newPath] = coverageJson[key];
                delete coverageJson[key];
            }
            if (newPath.includes('.vue') && coverageJson[newPath]?.inputSourceMap) {
                delete coverageJson[newPath].inputSourceMap
            }
        }
        resolve(coverageJson);
    });
};

// 辅助函数：获取 GitLab 项目详情
const getGitLabProjectDetails = async (projectId, token) => {
    const apiUrl = `${gitlabIp}/${projectId}`; // gitlabIp 已经是 /api/v4/projects
    console.log(`正在从以下地址获取 GitLab 项目详情: ${apiUrl}`);
    try {
        const response = await axios.get(apiUrl, { headers: { "PRIVATE-TOKEN": token } });
        console.log('GitLab API 响应状态:', response.status);
        // console.log('GitLab API 响应数据:', response.data);
        if (response.data && response.data.http_url_to_repo) {
            return response.data; // 返回完整的项目详情对象
        } else {
            console.error(`错误: 在项目 ID ${projectId} 的 GitLab API 响应中未找到 http_url_to_repo`);
            return null;
        }
    } catch (error) {
        console.error(`获取 GitLab 项目详情失败 (项目ID: ${projectId}):`, error.response ? error.response.data : error.message);
        return null;
    }
};

module.exports = {
    generateHtml,
    getHtmlList
};
