<!-- 代码覆盖率汇总  -->
<link rel="stylesheet" href="/css/bootstrap.css" />
<style>
  :root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --dark-color: #34495e;
    --light-color: #ecf0f1;
  }

  body {
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
  }

  .header {
    background-color: var(--dark-color);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
  }

  .header h1 {
    margin: 0;
    font-weight: 300;
  }

  .header p {
    margin-top: 0.5rem;
    opacity: 0.8;
  }

  .card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    transition: transform 0.2s;
  }

  .card:hover {
    transform: translateY(-5px);
  }

  .card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
  }

  .summary-card {
    text-align: center;
    padding: 1.5rem;
  }

  .summary-card h3 {
    margin-bottom: 0;
    font-size: 2.5rem;
    font-weight: 300;
  }

  .summary-card p {
    margin-top: 0.5rem;
    color: #6c757d;
  }

  .progress {
    height: 10px;
    border-radius: 5px;
    margin-top: 0.5rem;
  }

  .table-responsive {
    border-radius: 10px;
  }

  .table {
    margin-bottom: 0;
  }

  .table th {
    border-top: none;
    font-weight: 600;
  }

  .file-name {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
  }

  .badge-statements {
    background-color: var(--primary-color);
  }

  .badge-branches {
    background-color: var(--warning-color);
  }

  .badge-functions {
    background-color: var(--secondary-color);
  }

  .badge-lines {
    background-color: var(--dark-color);
  }

  .progress-statements .progress-bar {
    background-color: var(--primary-color);
  }

  .progress-branches .progress-bar {
    background-color: var(--warning-color);
  }

  .progress-functions .progress-bar {
    background-color: var(--secondary-color);
  }

  .progress-lines .progress-bar {
    background-color: var(--dark-color);
  }

  .chart-container {
    position: relative;
    height: 250px;
    width: 100%;
  }

  .search-box {
    margin-bottom: 1rem;
  }

  .file-row {
    cursor: pointer;
  }

  .file-row:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .file-details {
    display: none;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 5px;
    margin-top: 0.5rem;
  }

  .coverage-pill {
    display: inline-block;
    padding: 0.25em 0.6em;
    border-radius: 10rem;
    font-size: 0.75rem;
    font-weight: 700;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    color: #fff;
  }

  .coverage-high {
    background-color: var(--secondary-color);
  }

  .coverage-medium {
    background-color: var(--warning-color);
  }

  .coverage-low {
    background-color: var(--danger-color);
  }

  @media (max-width: 768px) {
    .file-name {
      max-width: 150px;
    }

    .summary-card h3 {
      font-size: 1.8rem;
    }
  }
</style>
<style>
  .container {
    max-width: 95%;
    margin: 20px auto;
    background-color: #fff;
    padding: 20px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }
  table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
  }
  th,
  td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
  }
  th {
    background-color: #f2f2f2;
    text-align: center;
  }
  tr:nth-child(even) {
    background-color: #f9f9f9;
  }
  tr:hover {
    background-color: #f1f1f1;
  }
  a {
    color: #007bff;
    text-decoration: none;
  }
  a:hover {
    text-decoration: underline;
  }
  .loading {
    position: fixed;
    width: 100%;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    text-align: center;
    line-height: 100vh;
    font-size: 28px;
    color: #fff;
    display: none;
    z-index: 99999999999;
  }
  .config-row {
    margin-bottom: 10px;
  }
  .metrics {
    width: 100%;
    margin: 10px auto;
  }
  ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }
  .metrics ul {
    display: flex;
    gap: 10px;
  }
  .metrics ul li {
    padding: 15px;
    flex: 1;
    text-align: center;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }
  .metrics-title {
    font-weight: bold;
  }
  .metric-value {
    font-size: 30px;
    color: #f44336;
    font-weight: bold;
  }
  .coverage {
    box-sizing: border-box;
  }
  .coverage .header {
    padding: 40px 20px;
  }
</style>
<div class="loading">加载中...</div>
<div class="container">
  <div class="config-row">
    <span>项目名称：</span>
    <select name="select" id="projectSelect"></select>
  </div>
  <div class="config-row">
    <span>覆盖范围：</span>
    <select name="select" id="range">
      <option value="false">全量</option>
      <option value="true">增量</option>
    </select>
  </div>
  <button onclick="checkTaskStatus()">判断是否有任务在执行</button>
  <button onclick="createTask()">创建任务</button>
  <button onclick="deleteTask()">删除任务</button>
  <button onclick="viewClick()">查看覆盖率报告</button>
  <button onclick="createClick()">生成覆盖率报告</button>
  <div class="metrics">
    <ul class="metrics-list"></ul>
  </div>
  <table>
    <thead>
      <tr>
        <th>序号</th>
        <th>任务ID</th>
        <th style="width: 200px">创建任务时间</th>
        <th style="width: 200px">生成覆盖率时间</th>
        <th style="width: 100px">任务状态</th>
        <th>查看覆盖率</th>
      </tr>
    </thead>
    <tbody class="tbody">
      <tr>
        <td colspan="6" style="text-align: center">暂无数据</td>
      </tr>
    </tbody>
  </table>
</div>
<script>
  const ip = "<%= APP_IP %>";
  const coverageTableBody = document.querySelector(".tbody");
  const url = new URL(window.location.href);
  const country = url.searchParams.get("country");
  // 获取项目配置列表
  fetch(`${ip}/api/getConfig`)
    .then((response) => response.json())
    .then(({ data }) => {
      let { projectList } = data.find((res) => country.includes(res.country));
      const selectElement = document.getElementById("projectSelect");
      selectElement.innerHTML = "";
      if (projectList && projectList.length > 0) {
        projectList.forEach(({ applicationName }) => {
          const option = document.createElement("option");
          option.value = applicationName;
          option.textContent = applicationName;
          selectElement.appendChild(option);
        });
      }
    });
  // 查看覆盖率报告
  let taskList = [];
  function viewClick() {
    const loadingElement = document.querySelector(".loading");
    const applicationName = document.getElementById("projectSelect").value;
    const range = document.getElementById("range").value;
    if (!country || !applicationName) return;
    loadingElement.style.display = "block";
    fetch(
      `${ip}/api/getHtmlDetail?country=${country}&applicationName=${applicationName}&isIncrement=${range}`
    )
      .then((response) => response.json())
      .then((res) => {
        // 清空现有表格内容
        coverageTableBody.innerHTML = "";
        let { data = [], msg } = res || {};
        if (!data?.length) {
          return;
        }
        taskList = [...data];
        // 动态生成表格行
        data.forEach((coverage, index) => {
          const row = document.createElement("tr");
          row.innerHTML = `
            <td>${index + 1}</td>
            <td>${coverage.taskId}</td>
            <td>${coverage.createTaskTime}</td>
            <td>${coverage.generateTime || "--"}</td>
            <td>${coverage.taskStatus === 1 ? "开启" : "关闭"}</td>
            <td>
            ${
              coverage.path
                ? `<a href="${coverage.path}" target="_blank">${coverage.path}</a>`
                : "--"
            }
            </td>
          `;
          coverageTableBody.appendChild(row);
        });
      })
      .catch((error) => console.error("请求失败:", error))
      .finally(() => {
        loadingElement.style.display = "none";
      });
  }
  viewClick();

  // 获取Html
  function createClick() {
    const loadingElement = document.querySelector(".loading");
    const applicationName = document.getElementById("projectSelect").value;
    const range = document.getElementById("range").value;
    if (!country || !applicationName) return;
    loadingElement.style.display = "block";
    fetch(
      `${ip}/api/createTaskHtml?country=${country}&applicationName=${applicationName}&isIncrement=${range}`
    )
      .then((response) => response.json())
      .then((res) => {
        console.log(res);
      })
      .catch((error) => console.error("请求失败:", error))
      .finally(() => {
        loadingElement.style.display = "none";
      });
  }

  // 汇总展示逻辑处理
  const metricsListDom = document.querySelector(".metrics-list");
  function handleSummary(coverageSummary) {
    let titleList = {
      statements: "语句覆盖率",
      branches: "分支覆盖率",
      functions: "函数覆盖率",
      lines: "行覆盖率"
    };
    metricsListDom.innerHTML = "";
    Object.entries(titleList).forEach(([key, value]) => {
      const row = document.createElement("li");
      let item = coverageSummary[key];
      row.innerHTML = `
            <div class="metrics-title">${value}</div>
            <div class="metric-value">${item.pct}%</div>
            <div class="metric-detail">已覆盖:${item.covered}/${item.total}</div>
          `;
      metricsListDom.appendChild(row);
    });
  }

  // 判断是否有任务在执行
  function checkTaskStatus() {
    const applicationName = document.getElementById("projectSelect").value;
    if (!country || !applicationName) return;
    fetch(
      `${ip}/api/hasRunningTask?country=${country}&applicationName=${applicationName}`
    )
      .then((response) => response.json())
      .then(({ data, msg }) => {
        alert(msg);
      });
  }

  // 创建任务
  function createTask() {
    const applicationName = document.getElementById("projectSelect").value;
    if (!country || !applicationName) return;
    fetch(
      `${ip}/api/createTask?country=${country}&applicationName=${applicationName}`
    )
      .then((response) => response.json())
      .then(({ data, msg }) => {
        alert(msg);
      });
  }

  function deleteTask() {
    console.log(taskList, "------------->taskList");
    const applicationName = document.getElementById("projectSelect").value;
    if (!country || !applicationName) return;
    let ids = taskList.map((item) => item.taskId);
    fetch(`${ip}/api/reqDeleteTask`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        ids,
        country,
        applicationName
      })
    })
      .then((response) => {
        console.log(response, "--------------->删除response");
        return response.json();
      })
      .then((data) => {
        console.log(data, "--------------->删除data");
      })
      .catch((err) => {
        console.error("Error sending coverage:", err);
      });
  }
</script>
