const { getFileNames, createDir, isSameDay } = require('./tools');
const { deleteTask } = require('./deleteTask');
// 判断是否有正在执行的任务
const hasTask = async function ({ country, applicationName }) {
    console.log("----->hasTask 获取文件夹名")
    return new Promise(async (resolve, reject) => {
        try {
            const { folders = [], maxTimestamp = '' } = await getFileNames(`./json/${country}/${applicationName}`)
            let flag = false;
            if (!folders.length) {
                // 没有任务文件夹
                flag = false
            } else {
                // 判断同一天是否创建过任务
                console.log(maxTimestamp, "----->hasTask 判断同一天是否创建过任务")
                flag = maxTimestamp ? await isSameDay(maxTimestamp, new Date().getTime()) : false
            }
            let msg = flag ? '有任务在执行' : '没有任务';
            resolve({ code: 1, data: flag, msg })
        } catch (e) {
            resolve({ code: 1, data: false, msg: '没有任务' })
        }
    })

};

// 创建任务
const createTask = async function ({ country, applicationName }) {
    return new Promise(async (resolve, reject) => {
        try {
            // 1.创建文件夹
            let tName = new Date().getTime();
            let json1 = `./json/${country}/${applicationName}/${tName}`
            let coverage1 = `./coverage/${country}/${applicationName}/${tName}/increment`
            let coverage2 = `./coverage/${country}/${applicationName}/${tName}/quantity`
            await createDir(json1)
            await createDir(coverage1)
            await createDir(coverage2)
            await taskNum({country, applicationName})
            resolve({ code: 1, data: true, msg: '创建成功' })
        } catch {
            resolve({ code: 401, data: false, msg: '创建失败' })
        }
    })
}

// 判断任务是否超个数，如果超过则删除最早的任务
const taskNum = async function ({ country, applicationName }) {
    return new Promise(async (resolve, reject) => {
        try {
            // 1.获取文件夹名
            console.log("----->taskNum判断任务是否超出个数")
            const { timestamps=[] } = await getFileNames(`./json/${country}/${applicationName}`)
            let delVal = 3
            let total = timestamps.length
            if(total>delVal){
                 console.log("----->hasTask超出个数删除,进行删除")
                let sliceNum = timestamps.length - delVal
                let ids = timestamps.sort((a, b) => {
                    return a -b
                }).slice(0, sliceNum)
               await deleteTask({country, applicationName,ids})
            }
        }finally{
            resolve({ code: 1, data: true, msg: '创建成功' })
        }
    })
}
module.exports = {
    hasTask,
    createTask
};
