// 公共方法
const fs = require("fs");
const path = require("path");
const { promisify } = require('util');
const { createCoverageMap } = require("istanbul-lib-coverage");
const simpleGit = require("simple-git");
const axios = require('axios');

// 创建文件目录
// targetDir 目标目录
const createDir = async function (targetDir) {
    if (typeof targetDir !== 'string' || !targetDir.trim()) {
        throw new Error('Invalid directory path: path must be a non-empty string');
    }
    try {
        await fs.promises.mkdir(targetDir, { recursive: true });
        return targetDir;
    } catch (error) {
        throw error;
    }
}

// 删除指定目录中的所有文件
// directoryPath - 要清空的目录路径
// 将 fs.readdir 和 fs.unlink 转为 Promise 版本
const readdirAsync = promisify(fs.readdir);
const unlinkAsync = promisify(fs.unlink);
async function clearDirectory(dirPath) {
    try {
        // 判断目录是否存在
        if (!fs.existsSync(dirPath)) {
            return true;
        }
        // 异步读取目录下的所有文件/子目录
        const files = await readdirAsync(dirPath);
        // 并行删除所有文件或递归清空子目录
        await Promise.all(
            files.map(async (file) => {
                const fullPath = path.join(dirPath, file);
                // 获取文件/目录信息
                const stats = await promisify(fs.lstat)(fullPath);
                if (stats.isDirectory()) {
                    // 如果是目录，递归删除
                    await promisify(fs.rm)(fullPath, { recursive: true, force: true });
                } else {
                    // 如果是文件，直接删除
                    await unlinkAsync(fullPath);
                }
            })
        );
        return true;
    } catch (error) {
        throw error;
    }
}

// 删除指定目录中的所有文件(删文件夹)
async function clearCatalogue(dirPath) {
    try {
        if (!fs.existsSync(dirPath)) return true;
        await fs.promises.rm(dirPath, { recursive: true, force: true });
        return true;
    } catch (error) {
        throw error;
    }
}

/**
 * 异步写入JSON数据到文件
 * @param {string} filePath - 目录路径以及名称
 * @param {object} data - 要写入的数据
 * @param {number} [indent=2] - JSON缩进空格数
 * @returns {Promise<void>}
 */
const writeJsonFile = function (filePath, data, indent = 2) {
    return new Promise((resolve, reject) => {
        fs.writeFile(
            filePath,
            JSON.stringify(data, null, indent),
            (err) => {
                if (err) {
                    reject(err.message);
                } else {
                    resolve(filePath);
                }
            }
        );
    });
}

// 合并提交的json文件
// const mergedJson = async function ({ dataPath }) {
//     return new Promise(async (resolve, reject) => {
//         try {
//             const coverageMap = createCoverageMap({});
//             // 1.读取目录下的所有文件
//             fs.readdir(dataPath, async (err, files) => {
//                 if (err) {
//                     reject(`${error.message}`)
//                 }
//                 // 2.筛选出JSON文件并且合并文件
//                 const jsonFiles = files.filter(
//                     (file) => path.extname(file).toLowerCase() === ".json"
//                 );
//                 jsonFiles.forEach((file) => {
//                     const filePath = path.join(dataPath, file);
//                     const data = JSON.parse(fs.readFileSync(filePath, "utf-8"));
//                     coverageMap.merge(data);
//                 });
//                 resolve({ coverageMap })
//             });
//         } catch (error) {
//             reject(`${error.message}`)
//         }
//     });
// }

const mergedJson = async function ({ dataPath }) {
    return new Promise((resolve, reject) => {
        try {
            // 1. Check if directory exists first
            if (!fs.existsSync(dataPath)) {
                return reject(new Error(`Directory does not exist: ${dataPath}`));
            }
            // 2. Read directory
            fs.readdir(dataPath, (err, files) => {
                if (err) {
                    return reject(new Error(`Error reading directory: ${err.message}`));
                }
                const coverageMap = createCoverageMap({});
                const jsonFiles = files.filter(
                    file => path.extname(file).toLowerCase() === ".json"
                );
                // Track any failed files
                const errors = [];
                jsonFiles.forEach(file => {
                    try {
                        const filePath = path.join(dataPath, file);
                        const data = JSON.parse(fs.readFileSync(filePath, "utf-8"));
                        coverageMap.merge(data);
                    } catch (fileError) {
                        errors.push(`Error processing ${file}: ${fileError.message}`);
                    }
                });

                if (errors.length > 0) {
                    console.warn('Some files could not be processed:', errors);
                }

                resolve({ coverageMap, warnings: errors });
            });
        } catch (error) {
            reject(new Error(`Unexpected error: ${error.message}`));
        }
    });
}

// 获取合并后的json文件
const getmergeJson = async function ({ country, applicationName, maxTimestamp }) {
    return new Promise(async (resolve, reject) => {
        let dataPath = `../../json/${country}/${applicationName}`;
        const dirPath = path.join(__dirname, `${dataPath}/${maxTimestamp}`);
        const mergedFilePath = path.join(dirPath, 'merged-coverage.json');
        try {
            if (fs.existsSync(mergedFilePath)) {
                const mergedData = JSON.parse(fs.readFileSync(mergedFilePath, 'utf-8'));
                console.log(`读取 merged-coverage.json 成功`);
                resolve({ country, applicationName, success: true, coverageJson: mergedData });
            } else {
                console.log(`merged-coverage.json 文件不存在`);
                resolve({ country, applicationName, success: true, coverageJson: null });
            }
        } catch (error) {
            reject(`读取 merged-coverage.json 文件错误: ${error.message}`);
        }
    });
};

// 通过git下载代码到本地, repoUrl: 仓库URL, localPath: 本地路径
const cloneAndGetCommits = function (repoUrl, localPath, branch = "production") {
    return new Promise(async (resolve, reject) => {
        try {
            const git = simpleGit();
            // 1. Clone 仓库（如果不存在）并指定分支
            if (!fs.existsSync(localPath)) {
                await git.clone(repoUrl, localPath, ["-b", branch]); // 关键修改：添加 -b 参数
                console.log(`仓库克隆成功(分支: ${branch})...`);
            } else {
                console.log("仓库已存在，直接读取...");
            }
            // 2. 切换到本地路径
            const repoGit = simpleGit({ baseDir: localPath });
            // 3. 检出指定分支（确保本地在正确分支）
            await repoGit.checkout(branch);
            // 4. 获取提交记录（限定当前分支）
            const commits = await repoGit.log([branch]);
            // 5. 拉取指定分支的最新代码
            await repoGit.pull("origin", branch); // 关键修改：明确指定远程和分支
            console.log(`${new Date()} 分支 ${branch} 的最新代码已拉取！`);
            let result = {
                branch: branch,
                commits: commits.all,
            };
            resolve(result);
        } catch (error) {
            reject(`Git 操作错误: ${error.message}`);
        }
    });
};

// 读文件
const readFile = function (filePath) {
    return new Promise((resolve, reject) => {
        fs.readFile(filePath, 'utf8', (err, data) => {
            if (err) {
                resolve({ code: 400, readData: null });
            } else {
                resolve({ code: 200, readData: data });
            }
        });
    });
}

// 判断文件是否存在
const checkFileExists = function (filePath, fileName) {
    return new Promise(async (resolve, reject) => {
        const pathName = path.join(filePath, fileName);
        if (fs.existsSync(pathName)) {
            resolve(true);
        } else {
            resolve(false);
        }
    })
}

// 获取项目信息
function getProjectInfo({ country, applicationName }) {
    return new Promise((resolve, reject) => {
        axios.get(`https://demo-process-flow-inner.yunlu-tech.cn/processflow/app/config/queryDetailByName?name=${applicationName}`, {
            headers: { "countryCode": country }
        }).then(response => {
            resolve(response.data)
        }).catch(error => {
            reject(error)
        });
    })
}

// 获取文件下面的所有文件目录名
const getFileNames = function (filePath) {
    return new Promise((resolve, reject) => {
        const folders = fs
            .readdirSync(filePath, { withFileTypes: true })
            .filter((dirent) => dirent.isDirectory())
        const timestamps = folders?.map(item => Number(item.name));
        const maxTimestamp = Math.max(...timestamps);
        resolve({
            folders,
            timestamps,
            maxTimestamp
        })
    });
}

// 判断时间戳是否为同一天
const isSameDay = function (timestamp1, timestamp2) {
    return new Promise((resolve, reject) => {
        if (typeof timestamp1 !== 'number' || typeof timestamp2 !== 'number') {
            reject('Invalid timestamp input');
        }
        const date1 = new Date(timestamp1);
        const date2 = new Date(timestamp2);
        resolve(
            date1.getFullYear() === date2.getFullYear() &&
            date1.getMonth() === date2.getMonth() &&
            date1.getDate() === date2.getDate()
        );
    });
}

// 把时间戳转化成年月日时分秒
const formatTimestamp = function (timestamp) {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

module.exports = {
    createDir,
    clearDirectory,
    clearCatalogue,
    writeJsonFile,
    mergedJson,
    getmergeJson,
    cloneAndGetCommits,
    readFile,
    checkFileExists,
    getProjectInfo,
    getFileNames,
    isSameDay,
    formatTimestamp
};
