/* 现代化的 Istanbul 代码覆盖率报告样式 */

/* 基础样式重置和字体 */
*, *:before, *:after {
  box-sizing: border-box;
}

body, html {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow: hidden; /* 去掉最外层滚动条 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #2d3748;
  line-height: 1.6;
  font-size: 14px;
}

/* 主容器 */
.wrapper {
  height: 100vh; /* 使用固定高度而不是最小高度 */
  background: #f7fafc;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 确保wrapper本身不产生滚动条 */
}

/* 内容区域 */
.content-area {
  flex: 1;
  overflow: hidden; /* 内容区域本身不滚动 */
  padding: 20px;
  display: flex;
  flex-direction: column;
}

/* 头部容器 - 将统计卡片和标题放在一起 */
.header-container {
  background: linear-gradient(135deg, #454ce1 0%, #646CFF 100%);
  border-radius: 0;
  margin-bottom: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 12px 20px;
  position: sticky;
  top: 0;
  z-index: 100;
}

/* 头部内容 - 水平布局 */
.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
}

/* 覆盖率统计卡片 */
.coverage-stats {
  display: grid;
  grid-template-columns: repeat(4, minmax(150px, 1fr));
  gap: 10px;
  flex: 1;
}

/* 页面头部样式 */
.page-header {
  color: white;
  background: transparent;
  padding: 0;
  margin: 0;
  flex-shrink: 0;
  text-align: left;
}

.page-header h1 {
  font-size: 22px;
  font-weight: 700;
  margin: 0 0 5px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header .breadcrumb {
  font-size: 14px;
  opacity: 0.9;
  font-weight: 500;
}

/* 覆盖率汇总表格样式 */
.coverage-summary {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
  font-size: 14px;
  background: white;
  display: table;
  table-layout: fixed;
}

.coverage-summary thead {
  position: sticky;
  top: 0;
  z-index: 10;
  background: linear-gradient(135deg, #454ce1 0%, #646CFF 100%);
}

.coverage-summary th {
  text-align: left;
  font-weight: 600;
  white-space: nowrap;
  border-bottom: 2px solid #e2e8f0;
  background: linear-gradient(135deg, #454ce1 0%, #646CFF 100%);
  color: white;
  padding: 12px 15px;
}

/* 调整文件列表表格的列宽 - 增加新增代码覆盖率列 */
.coverage-summary th.file {
  width: 30%; /* 文件名列减少到30% */
}

.coverage-summary th.pic {
  width: 10%; /* 图表列 */
}

.coverage-summary th.pct {
  width: 6%; /* 百分比列减少 */
}

.coverage-summary th.abs {
  width: 8%; /* 数量列减少 */
}

.coverage-summary td.file {
  width: 30%;
}

.coverage-summary td.pic {
  width: 10%;
}

.coverage-summary td.pct {
  width: 6%;
}

.coverage-summary td.abs {
  width: 8%;
}

.coverage-summary td {
  padding: 12px 15px;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: middle;
}

.coverage-summary tr:hover {
  background: #f8fafc;
  transition: background-color 0.2s ease;
}

.coverage-summary td.file {
  font-weight: 500;
  color: #2d3748;
}

.coverage-summary td.file a {
  color: #4299e1;
  text-decoration: none;
  transition: color 0.2s ease;
}

.coverage-summary td.file a:hover {
  color: #3182ce;
  text-decoration: underline;
}

.coverage-summary td.pct {
  font-weight: 600;
  text-align: right;
}

.coverage-summary td.abs {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 12px;
  color: #718096;
  text-align: right;
}

/* 覆盖率图表样式 */
.chart {
  width: 100px;
  height: 12px;
  border-radius: 6px;
  overflow: hidden;
  background: #e2e8f0;
  display: flex;
}

.cover-fill {
  height: 100%;
  background: linear-gradient(90deg, #38a169 0%, #48bb78 100%);
  transition: width 0.3s ease;
}

.cover-empty {
  height: 100%;
  background: #e2e8f0;
}

/* 覆盖率等级样式 */
.high {
  background: linear-gradient(90deg, #c6f6d5 0%, #9ae6b4 100%) !important;
  color: #22543d !important;
}

.medium {
  background: linear-gradient(90deg, #fef5e7 0%, #fbd38d 100%) !important;
  color: #744210 !important;
}

.low {
  background: linear-gradient(90deg, #fed7d7 0%, #feb2b2 100%) !important;
  color: #742a2a !important;
}

.empty {
  background: #f7fafc !important;
  color: #a0aec0 !important;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 12px 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #ffffff;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  backdrop-filter: blur(10px);
}

.stat-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 1);
}

.stat-card .stat-label {
  font-size: 11px;
  color: #4a5568;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 3px;
}

.stat-card .stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
}

.stat-card .stat-fraction {
  font-size: 11px;
  color: #718096;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

/* 代码表格样式 - IDE 风格 */
.code-container {
  background: #ffffff;
  border-radius: 8px;
  overflow-y: auto; /* 恢复滚动功能 */
  flex: 1; /* 使用flex布局占满剩余空间 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', 'Consolas', 'Courier New', monospace;
  border: 1px solid #e1e4e8;
  position: relative;
}

table.coverage {
  width: 100%;
  table-layout: fixed; /* 固定表格布局，确保列宽一致 */
  border-collapse: collapse;
  margin: 0;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.45;
  background: #ffffff;
  color: #24292e;
}

table.coverage tr {
  display: flex;
  width: 100%;
  /* 去掉横向边框 */
}

table.coverage td {
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center; /* 垂直居中对齐 */
  border: none;
  height: 24px; /* 增加行高以改善对齐 */
  border-right: 1px solid #e1e4e8;
}

table.coverage td:last-child {
  border-right: none;
}

table.coverage td.line-count {
  justify-content: flex-end; /* 右对齐 */
  background: #f6f8fa;
  color: #586069;
  font-weight: 400;
  width: 50px;
  min-width: 50px;
  max-width: 50px;
  flex-shrink: 0;
  user-select: none;
  padding: 0 10px;
  font-size: 12px;
}

table.coverage td.line-coverage {
  justify-content: center; /* 居中对齐 */
  width: 80px;
  min-width: 80px;
  max-width: 80px;
  flex-shrink: 0;
  background: #f6f8fa;
  padding: 0 8px;
}

table.coverage td.text {
  flex: 1;
  min-width: 0;
  padding: 0 8px;
  font-family: inherit;
  white-space: pre;
  background: #ffffff;
  color: #24292e;
  overflow: hidden; /* 防止代码溢出 */
  word-wrap: break-word;
}

/* 语句覆盖率样式 - 三色系统 */
.cstat-yes {
  background-color: #c6f6d5 !important; /* 绿色 - 完全覆盖 */
  color: #22543d !important;
  padding: 1px 2px;
  border-radius: 2px;
}

.cstat-partial {
  background-color: #fff3c4 !important; /* 黄色 - 部分覆盖 */
  color: #f57f17 !important;
  padding: 1px 2px;
  border-radius: 2px;
}

.cstat-no {
  background-color: #fed7d7 !important; /* 红色 - 未覆盖 */
  color: #742a2a !important;
  padding: 1px 2px;
  border-radius: 2px;
}

/* 函数覆盖率样式 - 三色系统 */
.fstat-yes {
  background-color: #c6f6d5 !important; /* 浅绿色 - 完全覆盖 */
  color: #22543d !important;
  padding: 1px 2px;
  border-radius: 2px;
}

.fstat-partial {
  background-color: #fff3c4 !important; /* 浅黄色 - 部分覆盖 */
  color: #f57f17 !important;
  padding: 1px 2px;
  border-radius: 2px;
}

.fstat-no {
  background-color: #fed7d7 !important; /* 浅红色 - 未覆盖 */
  color: #742a2a !important;
  padding: 1px 2px;
  border-radius: 2px;
}

/* 分支覆盖率样式 - 三色系统 */
.cbranch-yes {
  background-color: #d4edda !important; /* 绿色 - 完全覆盖 */
  color: #155724 !important;
  padding: 1px 2px;
  border-radius: 2px;
}

.cbranch-partial {
  background-color: #fff3c4 !important; /* 黄色 - 部分覆盖 */
  color: #f57f17 !important;
  padding: 1px 2px;
  border-radius: 2px;
}

.cbranch-no {
  background-color: #f8d7da !important; /* 红色 - 未覆盖 */
  color: #721c24 !important;
  padding: 1px 2px;
  border-radius: 2px;
}

.cbranch-skip {
  background-color: #e2e3e5 !important;
  color: #6c757d !important;
  padding: 1px 2px;
  border-radius: 2px;
}

/* 代码行整行背景样式 */
table.coverage td.text {
  position: relative;
}

/* 移除代码行背景染色，保持干净的代码显示 */
table.coverage tr:has(.cline-yes) td.text,
table.coverage tr:has(.cstat-yes) td.text,
table.coverage tr:has(.cstat-partial) td.text,
table.coverage tr:has(.cline-no) td.text,
table.coverage tr:has(.cstat-no) td.text,
table.coverage tr:has(.cline-neutral) td.text {
  background: #ffffff !important;
}

/* 如果浏览器不支持 :has() 选择器，使用备用方案 */
@supports not selector(:has(*)) {
  /* 通过 JavaScript 动态添加类名的备用方案 - 保持干净的代码显示 */
  table.coverage tr.covered-yes td.text,
  table.coverage tr.covered-no td.text,
  table.coverage tr.covered-neutral td.text {
    background: #ffffff !important;
  }
}

/* 代码行样式 */
table.coverage td.text pre {
  margin: 0;
  padding: 0;
  background: transparent !important;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
  display: inline-block;
  vertical-align: middle;
}

/* 移除重复的样式定义，保持上面的完整定义 */

/* 覆盖率指示器样式 */
table.coverage span.cline-any {
  display: inline-block;
  padding: 2px 6px;
  margin: 0;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
  width: 60px;
  text-align: center;
  white-space: nowrap;
  border: 1px solid transparent;
  vertical-align: middle;
}

/* 新增语句样式 - 根据覆盖状态显示不同颜色 */
table.coverage span.cline-new {
  background: #c6f6d5; /* 默认使用已覆盖的绿色 */
  color: #22543d;
  border-color: #9ae6b4;
}

/* 确保新增语句的行背景色也根据覆盖状态显示 */
tr.new-statement-covered {
  background: rgba(198, 246, 213, 0.1) !important; /* 浅绿色背景 */
}

tr.new-statement-uncovered {
  background: rgba(254, 215, 215, 0.1) !important; /* 浅红色背景 */
}

/* 已覆盖状态样式 */
table.coverage span.cline-yes {
  background: #c6f6d5;
  color: #22543d;
  border-color: #9ae6b4;
}

/* 部分覆盖状态样式 */
table.coverage span.cline-partial {
  background: #fff3c4;
  color: #f57f17;
  border-color: #f6e05e;
}

/* 未覆盖状态样式 */
table.coverage span.cline-no {
  background: #fed7d7;
  color: #742a2a;
  border-color: #feb2b2;
}

/* 中性状态样式 - 无背景色 */
table.coverage span.cline-neutral {
  background: transparent;
  color: #4a5568;
  border: none;
}

/*无需覆盖代码样式*/
table.coverage span.cline-null {
  color: #000;
}

/* 确保代码高亮不被覆盖 */
table.coverage .prettyprint {
  background: transparent !important;
  border: none !important;
  padding: 8px 12px !important;
  margin: 0 !important;
}

/* 启用代码高亮，保持语法着色 */

/* 覆盖率指示器 */
.coverage-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.coverage-high { background: #38a169; }
.coverage-medium { background: #d69e2e; }
.coverage-low { background: #e53e3e; }

/* 分支覆盖率样式 */
.missing-if-branch {
  display: inline-block;
  margin-right: 8px;
  border-radius: 6px;
  padding: 2px 6px;
  background: #2d3748;
  color: #ffd700;
  font-size: 11px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
  }

  .coverage-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .page-header {
    text-align: center;
  }
}

@media (max-width: 768px) {
  .wrapper {
    padding: 15px;
  }

  .header-container {
    padding: 15px 20px;
  }

  .coverage-stats {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .page-header h1 {
    font-size: 24px;
  }

  .stat-card {
    padding: 15px;
  }

  .stat-card .stat-value {
    font-size: 20px;
  }

  table.coverage td {
    padding: 6px 8px;
  }

  table.coverage td.text {
    padding-left: 12px;
    font-size: 12px;
  }
}

/* 工具提示样式 */
.tooltip {
  position: relative;
  cursor: help;
}

.tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #2d3748;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.code-container {
  animation: fadeIn 0.5s ease-out;
}

.stat-card {
  animation: fadeIn 0.5s ease-out;
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 打印样式 */
@media print {
  body {
    background: white !important;
  }

  .page-header {
    background: #4299e1 !important;
    -webkit-print-color-adjust: exact;
  }

  .stat-card {
    box-shadow: none !important;
    border: 1px solid #e2e8f0 !important;
  }
}

/* 辅助类 */
.text-center { text-align: center; }
.text-right { text-align: right; }
.font-bold { font-weight: 700; }
.font-medium { font-weight: 500; }
.text-sm { font-size: 12px; }
.text-lg { font-size: 16px; }
.mb-4 { margin-bottom: 16px; }
.mt-4 { margin-top: 16px; }
.p-4 { padding: 16px; }

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .cline-yes {
    background: #000 !important;
    color: #fff !important;
  }

  .cline-no {
    background: #fff !important;
    color: #000 !important;
    border: 2px solid #000 !important;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    color: #e2e8f0;
  }

  .wrapper {
    background: #1a202c;
  }

  .code-container,
  .stat-card {
    background: #2d3748;
    color: #e2e8f0;
  }

  table.coverage td {
    border-bottom-color: #4a5568;
  }

  table.coverage td.line-count,
  table.coverage td.line-coverage {
    background: #1a202c;
    color: #a0aec0;
  }

  table.coverage td.text {
    background: #2d3748;
  }
}

/* 代码行背景色根据覆盖状态显示 */
table.coverage tr.covered-yes {
  background: rgba(198, 246, 213, 0.1) !important; /* 浅绿色背景 */
}

table.coverage tr.covered-partial {
  background: rgba(255, 243, 196, 0.1) !important; /* 浅黄色背景 */
}

table.coverage tr.covered-no {
  background: rgba(254, 215, 215, 0.1) !important; /* 浅红色背景 */
}

table.coverage tr.covered-neutral {
  background: #ffffff !important; /* 白色背景 */
}

/* 确保单元格背景透明，以便行背景可见 */
table.coverage tr.covered-yes td,
table.coverage tr.covered-partial td,
table.coverage tr.covered-no td,
table.coverage tr.covered-neutral td {
  background: transparent !important;
}

/* 代码高亮保持可读性 */
table.coverage tr.covered-yes .prettyprint,
table.coverage tr.covered-partial .prettyprint,
table.coverage tr.covered-no .prettyprint,
table.coverage tr.covered-neutral .prettyprint {
  background: transparent !important;
  padding: 8px 12px !important;
  margin: 0 !important;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
  font-size: 13px;
  line-height: 1.5;
}

/* 启用代码语法高亮 */
table.coverage .prettyprint .kwd { color: #0000ff; font-weight: bold; } /* 关键字 - 蓝色 */
table.coverage .prettyprint .typ { color: #2b91af; } /* 类型 - 深蓝色 */
table.coverage .prettyprint .lit { color: #800000; } /* 字面量 - 深红色 */
table.coverage .prettyprint .pun { color: #000000; } /* 标点符号 - 黑色 */
table.coverage .prettyprint .pln { color: #000000; } /* 普通文本 - 黑色 */
table.coverage .prettyprint .tag { color: #800000; } /* HTML标签 - 深红色 */
table.coverage .prettyprint .atn { color: #ff0000; } /* 属性名 - 红色 */
table.coverage .prettyprint .atv { color: #0000ff; } /* 属性值 - 蓝色 */
table.coverage .prettyprint .dec { color: #800080; } /* 声明 - 紫色 */
table.coverage .prettyprint .var { color: #008080; } /* 变量 - 青色 */
table.coverage .prettyprint .fun { color: #dd4a68; } /* 函数 - 粉红色 */
table.coverage .prettyprint .str { color: #008000; } /* 字符串 - 绿色 */
table.coverage .prettyprint .com { color: #008000; font-style: italic; } /* 注释 - 绿色斜体 */
/* 覆盖所有可能的蓝色背景 */
tr[style*="background-color: rgb(187, 222, 251)"],
tr[style*="background-color: #bbdefb"],
tr[style*="background-color: rgba(187, 222, 251"],
tr[style*="background: rgb(187, 222, 251)"],
tr[style*="background: #bbdefb"],
tr[style*="background: rgba(187, 222, 251"],
tr[style*="background-color: rgb(224, 242, 254)"],
tr[style*="background-color: #e0f2fe"],
tr[style*="background-color: rgba(224, 242, 254"],
tr[style*="background: rgb(224, 242, 254)"],
tr[style*="background: #e0f2fe"],
tr[style*="background: rgba(224, 242, 254"] {
  background-color: rgba(198, 246, 213, 0.1) !important; /* 替换为浅绿色背景 */
}

/* 覆盖所有可能的蓝色背景的单元格 */
td[style*="background-color: rgb(187, 222, 251)"],
td[style*="background-color: #bbdefb"],
td[style*="background-color: rgba(187, 222, 251"],
td[style*="background: rgb(187, 222, 251)"],
td[style*="background: #bbdefb"],
td[style*="background: rgba(187, 222, 251"],
td[style*="background-color: rgb(224, 242, 254)"],
td[style*="background-color: #e0f2fe"],
td[style*="background-color: rgba(224, 242, 254"],
td[style*="background: rgb(224, 242, 254)"],
td[style*="background: #e0f2fe"],
td[style*="background: rgba(224, 242, 254"] {
  background-color: rgba(198, 246, 213, 0.1) !important; /* 替换为浅绿色背景 */
}

/* 覆盖所有可能的蓝色背景的span元素 */
span[style*="background-color: rgb(187, 222, 251)"],
span[style*="background-color: #bbdefb"],
span[style*="background-color: rgba(187, 222, 251"],
span[style*="background: rgb(187, 222, 251)"],
span[style*="background: #bbdefb"],
span[style*="background: rgba(187, 222, 251"],
span[style*="background-color: rgb(224, 242, 254)"],
span[style*="background-color: #e0f2fe"],
span[style*="background-color: rgba(224, 242, 254"],
span[style*="background: rgb(224, 242, 254)"],
span[style*="background: #e0f2fe"],
span[style*="background: rgba(224, 242, 254"] {
  background-color: #c6f6d5 !important; /* 替换为绿色背景 */
  color: #22543d !important;
  border-color: #9ae6b4 !important;
}
/* 确保所有行背景色都根据覆盖状态显示 */
tr {
  background-color: #ffffff !important; /* 默认白色背景 */
}

tr.covered-yes, tr.new-statement-covered {
  background-color: rgba(198, 246, 213, 0.1) !important; /* 浅绿色背景 */
}

tr.covered-no, tr.new-statement-uncovered {
  background-color: rgba(254, 215, 215, 0.1) !important; /* 浅红色背景 */
}

/* 覆盖任何可能的内联样式 */
tr[style*="background"], td[style*="background"], span[style*="background"] {
  background-color: inherit !important;
}

/* 特别处理新增语句的行 */
tr.new-statement {
  background-color: rgba(198, 246, 213, 0.1) !important; /* 默认使用浅绿色背景 */
}

/* 覆盖所有可能的蓝色背景类 */
.blue-bg, .new-statement-bg, .increment-bg, .added-bg, .modified-bg {
  background-color: rgba(198, 246, 213, 0.1) !important; /* 替换为浅绿色背景 */
}

