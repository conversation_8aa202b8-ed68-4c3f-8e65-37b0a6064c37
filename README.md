```
npm run start

步骤
node端
第一步
1.获取coverage.json
2.通过路径，获取代码目录，路径
3.生成源码目录，拉取git源码
4.生成html预览文件

第二步
1.客户端提交coverage.json=》后端=》node服务拉取coverage.json
2.coverage.json合并成一个文件，保存到本地（生产运行的镜像和测试环境最后一个镜像是否一致），如果不一致，则覆盖本地文件
3.通过国家+项目名访问生成的html文件
1.客户端获取coverage.json（接口提交，node端访问链接下载到本地）
2.把coverage根不同项目，保存到不同的目录
3.运行node端，访问html预览文件
4.展示代码覆盖率

第三步
1.颜色渲染准确
2.增量

下载服务器文件
https://***********/file/download?file=/data/app/front/yl-jis-sqs-coverage-front/logs
日志预览：https://***********/coverage/output.log

接口
1.提交覆盖率发送到服务
url: /api/coverage
 请求方式： post
入参：国家country  项目名称applicationName   覆盖率数据data

2.获取国家、项目，ip下拉列表
 url: /api/getConfig
 请求方式： get
入参：无

3.生成覆盖率html
 url: /api/createTaskHtml?country=${country}&applicationName=${applicationName}&isIncrement=${isIncrement}
 请求方式： get
入参：国家country  项目名称applicationName 全量覆盖isIncrement

4.查询生成覆盖率
 url: /api/getHtmlDetail?country=${country}&applicationName=${applicationName}&isIncrement=${isIncrement}
 请求方式： get
入参：国家country  项目名称applicationName 全量覆盖isIncrement

5.判断是否有覆盖率任务
 url: /api/hasRunningTask?country=${country}&applicationName=${applicationName}
 请求方式： get
入参：国家country  项目名称applicationName

6.创建覆盖率任务
 url: /api/createTask?country=${country}&applicationName=${applicationName}
 请求方式： get
入参：国家country  项目名称applicationName

7.删除任务
 url: /api/reqDeleteTask
 请求方式： post
 入参：国家country  项目名称applicationName 任务ids:[]

```

```
三次重构，根据任务进行操作一些列操作
1.提交json，根据最新的任务id，进行提交（增）
2.删除任务,根据任务id删除（删）
3.生成html，根据最新的任务id，进行生成（改）
4.生成html，根据最新的任务id，进行查询（查）
```

```
测试途中，中途加代码逻辑，导致覆盖率数据错误优化方案
优化方案：
第一步：jms端每次提交json的时候，根据镜像Id判断是否为最新镜像（或者参考架构强制刷新逻辑），
如果不是最新镜像，则强制刷新页面，不需要提交本次json，所以提交的都是最新的json数据
第二步：如果需要刷新页面，获取从当前测试commit到最新commit的文件差异，并把差异文件路径提交到node服务端（需要新增接口，删除差异文件路径）
第三步：node服务端根据提交过来的路径进行删除原来json提交的文件路径
```

```
颗粒度更细，针对新增去做覆盖
渲染错位
```

```
测试途中，中途加代码逻辑，导致覆盖率数据错误优化方案
优化方案：
第一步：走架构强制刷新
第二步：jms端获取commit节点，并提把节点提交到node服务端
第三步：node服务端根据commit节点，node获取commit节点和历史提交的进行对比，如果有差异，则获取git差异文件名，并把历史josn文件中差异文件进行删除，然后再次合并
```

```
测试途中，中途加代码逻辑，导致覆盖率数据错误优化方案
node端处理逻辑优化方案
1.获取json，并获取commitId，存储起来{jos哈希值:commitId}
2.json里面的has值如果强刷会改变，如果has改变，则获取git节点差异文件，把差异文件删除，再合并
```
