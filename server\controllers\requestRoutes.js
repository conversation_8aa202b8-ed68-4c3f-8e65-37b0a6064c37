const express = require('express');
const router = express.Router();
const { saveJson } = require('../utils/saveJson');
const configJson = require('../config/requestConfig');
const { generateHtml, getHtmlList } = require('../utils/createHtml');
const { hasTask, createTask } = require('../utils/createTask');
const { deleteTask } = require('../utils/deleteTask');
const { formatTimestamp } = require('../utils/tools');


// 剔除uat-front
const removeUatFront = function (reqObj) {
    const { applicationName } = reqObj;
    if (typeof applicationName === 'string' && applicationName.startsWith('uat-front-')) {
      const rest = applicationName.replace(/^uat-front-[^-]+-/, '');
      return {
        ...reqObj,
        applicationName: rest
      };
    }
    return reqObj;
}
// 获取国家、项目，ip下拉列表
const getConfig = async (req, res) => {
    console.log(formatTimestamp(new Date().getTime()), "--------------------->/getConfig开始请求时间<---------------------")
    try {
        res.status(200).json({ code: 1, data: configJson, msg: '请求成功' });
    } catch (error) {
        res.status(200).json({ code: 401, msg: '系统内部错误' });
    }
}
router.get('/getConfig', getConfig);

// 提交coverage.json 接口
const createHandler = async (req, res) => {
    console.log(formatTimestamp(new Date().getTime()), "--------------------->/coverage开始请求时间<---------------------")
    try {
        console.log("----->开始提交coverage.json")
        let result = await saveJson(removeUatFront(req.body || {}))
        res.status(200).json(result);
        console.log("----->提交成功coverage.json")
    } catch (error) {
        res.status(200).json({ code: 401, data: false, msg: '系统内部错误' });
    }
};
router.post('/coverage', createHandler);

// 判断是否有正在执行的任务
router.get('/hasRunningTask', async (req, res) => {
    console.log(formatTimestamp(new Date().getTime()), "--------------------->/hasRunningTask开始请求时间<---------------------")
    try {
        console.log("----->开始判断是否有任务")
        let result = await hasTask(removeUatFront(req.query || {}))
        console.log("----->判断成功")
        res.status(200).json(result);
    } catch (error) {
        res.status(200).json(error);
    }
});

// 创建任务
router.get('/createTask', async (req, res) => {
    console.log(formatTimestamp(new Date().getTime()), "--------------------->/createTask开始请求时间<---------------------")
    try {
        console.log("----->开始创建任务")
        let result = await createTask(removeUatFront(req.query || {}))
        console.log("----->创建成功")
        res.status(200).json(result);
    } catch (error) {
        res.status(200).json(error);
    }
});

// 生成任务Html
router.get('/createTaskHtml', async (req, res) => {
    console.log(formatTimestamp(new Date().getTime()), "--------------------->/createTaskHtml开始请求时间<---------------------")
    try {
        console.log("----->createTaskHtml 开始生成html")
        let result = await generateHtml(removeUatFront(req.query || {}))
        console.log("----->createTaskHtml 生成html成功")
        res.status(200).json(result);
    } catch (error) {
        res.status(200).json(error);
    }
});

// 查看任务html
router.get('/getHtmlDetail', async (req, res) => {
    console.log(formatTimestamp(new Date().getTime()), "--------------------->/getHtmlDetail开始请求时间<---------------------")
    try {
        console.log("----->查看任务详情")
        let result = await getHtmlList(removeUatFront(req.query || {}))
        console.log("----->查看任务详情成功")
        res.status(200).json(result);
    } catch (error) {
        res.status(200).json(error);
    }
})

// 删除任务
router.post('/reqDeleteTask', async (req, res) => {
    console.log(formatTimestamp(new Date().getTime()), "--------------------->/deleteTask开始请求时间<---------------------")
    try {
        console.log("----->删除任务开始")
        let result = await deleteTask(removeUatFront(req.body || {}))
        console.log("----->删除任务成功")
        res.status(200).json(result);
    } catch (error) {
        res.status(200).json(error);
    }
})

module.exports = router;
