// 在页面加载时修复覆盖率显示
document.addEventListener('DOMContentLoaded', function() {
  // 修复覆盖率显示
  const fixCoverageDisplay = () => {
    // 获取所有行
    const rows = document.querySelectorAll('table.coverage tr');

    rows.forEach(row => {
      // 获取覆盖率指示器
      const coverageIndicator = row.querySelector('.line-coverage .cline-any');
      if (!coverageIndicator) return;

      // 检查覆盖率指示器的类名
      const isCovered = coverageIndicator.classList.contains('cline-yes');
      const isUncovered = coverageIndicator.classList.contains('cline-no');

      // 根据覆盖率指示器的类名设置行的背景色
      if (isCovered) {
        // 已覆盖，使用浅绿色背景
        row.style.backgroundColor = 'rgba(198, 246, 213, 0.1)';
        // 确保文本单元格的背景色是透明的
        const textCell = row.querySelector('.text');
        if (textCell) {
          textCell.style.backgroundColor = 'transparent';
        }
      } else if (isUncovered) {
        // 未覆盖，使用浅红色背景
        row.style.backgroundColor = 'rgba(254, 215, 215, 0.1)';
        // 确保文本单元格的背景色是透明的
        const textCell = row.querySelector('.text');
        if (textCell) {
          textCell.style.backgroundColor = 'transparent';
        }
      }

      // 如果是新增语句，修改显示文本
      if (coverageIndicator.textContent === '新增语句') {
        if (isCovered) {
          coverageIndicator.textContent = '已覆盖';
        } else if (isUncovered) {
          coverageIndicator.textContent = '未覆盖';
        }
      }
    });

    // 移除所有蓝色背景
    const allElements = document.querySelectorAll('*');
    allElements.forEach(el => {
      const style = window.getComputedStyle(el);
      const bgColor = style.backgroundColor;

      // 检查是否为蓝色背景
      if (bgColor.includes('rgb(187, 222, 251)') ||
          bgColor.includes('rgb(224, 242, 254)') ||
          bgColor.includes('#bbdefb') ||
          bgColor.includes('#e0f2fe')) {

        // 获取所在行的覆盖率指示器
        let row = el;
        while (row && row.tagName !== 'TR') {
          row = row.parentElement;
        }

        if (row) {
          const coverageIndicator = row.querySelector('.line-coverage .cline-any');
          if (coverageIndicator) {
            const isCovered = coverageIndicator.classList.contains('cline-yes');

            if (isCovered) {
              // 已覆盖，使用浅绿色背景
              el.style.backgroundColor = 'rgba(198, 246, 213, 0.1)';
            } else {
              // 未覆盖，使用浅红色背景
              el.style.backgroundColor = 'rgba(254, 215, 215, 0.1)';
            }
          } else {
            // 默认使用浅绿色背景
            el.style.backgroundColor = 'rgba(198, 246, 213, 0.1)';
          }
        } else {
          // 默认使用浅绿色背景
          el.style.backgroundColor = 'rgba(198, 246, 213, 0.1)';
        }
      }
    });
  };

  // 初始执行一次
  setTimeout(fixCoverageDisplay, 100);

  // 每秒检查一次，以防有动态添加的元素
  setInterval(fixCoverageDisplay, 1000);
});
