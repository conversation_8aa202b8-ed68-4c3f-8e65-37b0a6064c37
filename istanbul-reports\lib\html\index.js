'use strict'
/*
 Copyright 2012-2015, Yahoo Inc.
 Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.
 */
const fs = require('fs')
const path = require('path')
const html = require('html-escaper')
const { ReportBase } = require('istanbul-lib-report')
const annotator = require('./annotator')


function htmlHead(details) {
  return `
<head>
    <title>Code coverage report for ${html.escape(details.entity)}</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="${html.escape(details.prettify.css)}" />
    <link rel="stylesheet" href="${html.escape(details.base.css)}" />
    <link rel="stylesheet" href="blue-override.css" />
    <link rel="shortcut icon" type="image/x-icon" href="${html.escape(
    details.favicon
  )}" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(${html.escape(details.sorter.image)});
        }
        /* 内联样式覆盖所有蓝色背景 */
        [style*="background-color: rgb(187, 222, 251)"],
        [style*="background-color: #bbdefb"],
        [style*="background-color: rgba(187, 222, 251"],
        [style*="background: rgb(187, 222, 251)"],
        [style*="background: #bbdefb"],
        [style*="background: rgba(187, 222, 251"],
        [style*="background-color: rgb(224, 242, 254)"],
        [style*="background-color: #e0f2fe"],
        [style*="background-color: rgba(224, 242, 254"],
        [style*="background: rgb(224, 242, 254)"],
        [style*="background: #e0f2fe"],
        [style*="background: rgba(224, 242, 254"] {
          background-color: rgba(198, 246, 213, 0.1) !important; /* 替换为浅绿色背景 */
        }
    </style>
</head>
    `
}

function headerTemplate(details) {
  function metricsTemplate({ pct, covered, total }, kind) {
    const getColorClass = (percentage) => {
      if (percentage >= 80) return 'coverage-high'
      if (percentage >= 60) return 'coverage-medium'
      return 'coverage-low'
    }

    return `
            <div class='stat-card'>
                <div class='stat-label'>${kind}</div>
                <div class='stat-value'>
                    <span class='coverage-indicator ${getColorClass(pct)}'></span>
                    ${pct}%
                </div>
                <div class='stat-fraction'>${covered}/${total}</div>
            </div>
        `
  }

  function skipTemplate(metrics) {
    const statements = metrics.statements.skipped
    const branches = metrics.branches.skipped
    const functions = metrics.functions.skipped

    const countLabel = (c, label, plural) =>
      c === 0 ? [] : `${c} ${label}${c === 1 ? '' : plural}`
    const skips = [].concat(
      countLabel(statements, 'statement', 's'),
      countLabel(functions, 'function', 's'),
      countLabel(branches, 'branch', 'es')
    )

    if (skips.length === 0) {
      return ''
    }

    return `
            <div class='stat-card' style='border-left-color: #a0aec0;'>
                <div class='stat-label'>忽略项</div>
                <div class='stat-value' style='color: #718096;'>${skips.join(', ')}</div>
            </div>
        `
  }

  // 计算新增代码覆盖率
  function calculateIncrementMetrics(details) {
    // 如果有传入的新增代码覆盖率数据，直接使用
    if (details.incrementMetrics) {
      return details.incrementMetrics
    }

    // 如果没有传入数据，返回默认值
    return { pct: 0, covered: 0, total: 0 }
  }

  return `
<!doctype html>
<html lang="zh-CN">
${htmlHead(details)}
<body>
<div class='wrapper'>
    <div class='header-container'>
        <div class='header-content'>
            <div class='page-header'>
                <h1>代码覆盖率报告</h1>
                <div class='breadcrumb'>${details.pathHtml}</div>
            </div>

            <div class='coverage-stats' style='display: flex; flex-wrap: wrap; gap: 16px; align-items: center;'>
                ${metricsTemplate(details.metrics.statements, '语句覆盖率')}
                ${metricsTemplate(details.metrics.branches, '分支覆盖率')}
                ${metricsTemplate(details.metrics.functions, '函数覆盖率')}
                ${metricsTemplate(details.metrics.lines, '行覆盖率')}
                ${metricsTemplate(calculateIncrementMetrics(details), '🆕 新增代码覆盖率')}
                ${skipTemplate(details.metrics)}
            </div>
        </div>
    </div>

    <div class="content-area">
        <div class="mb-4">
            <p style="color: #718096; font-size: 12px; margin: 0;">
                💡 按 <kbd>n</kbd> 或 <kbd>j</kbd> 转到下一个未覆盖的块，按 <kbd>b</kbd>、<kbd>p</kbd> 或 <kbd>k</kbd> 转到上一个块。
              <div id="colorLegend" style="display: flex; align-items: center; gap: 15px; font-size: 14px; color: #4a5568; flex-wrap: wrap;">
                        <span style="font-weight: 500;">代码行颜色说明：</span>
                        <div style="display: flex; align-items: center; gap: 6px;">
                            <div style="width: 16px; height: 16px; background-color: #c6f6d5; border-radius: 3px; border: 1px solid #9ae6b4;"></div>
                            <span>已覆盖</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 6px;">
                            <div style="width: 16px; height: 16px; background-color: #fff3c4; border-radius: 3px; border: 1px solid #f6e05e;"></div>
                            <span>部分覆盖</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 6px;">
                            <div style="width: 16px; height: 16px; background-color: #fed7d7; border-radius: 3px; border: 1px solid #feb2b2;"></div>
                            <span>未覆盖</span>
                        </div>
                    </div>
            </p>
            <template id="filterTemplate">
                <div style="margin-top: 10px; display: flex; align-items: center; gap: 20px; flex-wrap: wrap;">
                    <div>
                        <label for="fileSearch" style="color: #4a5568; font-weight: 500;">筛选文件：</label>
                        <input type="search" id="fileSearch" style="margin-left: 10px; padding: 6px 10px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px;">
                    </div>
                    <div style="display: flex; align-items: center; gap: 15px; font-size: 14px; color: #4a5568; margin-top: 10px;">
                        <span style="font-weight: 500;">覆盖率等级：</span>
                        <div style="display: flex; align-items: center; gap: 6px;">
                            <div style="width: 16px; height: 16px; background: linear-gradient(90deg, #c6f6d5 0%, #9ae6b4 100%); border-radius: 3px;"></div>
                            <span>高覆盖率 (≥80%)</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 6px;">
                            <div style="width: 16px; height: 16px; background: linear-gradient(90deg, #fef5e7 0%, #fbd38d 100%); border-radius: 3px;"></div>
                            <span>中等覆盖率 (60-79%)</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 6px;">
                            <div style="width: 16px; height: 16px; background: linear-gradient(90deg, #fed7d7 0%, #feb2b2 100%); border-radius: 3px;"></div>
                            <span>低覆盖率 (<60%)</span>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    `
}

function footerTemplate(details) {
  return `
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
        <script src="${html.escape(details.prettify.js)}"></script>
        <script>
            window.onload = function () {
                prettyPrint();

                // 控制代码颜色说明的显示/隐藏
                function toggleColorLegend() {
                    const colorLegend = document.getElementById('colorLegend');
                    const fileSearch = document.getElementById('fileSearch');

                    if (colorLegend) {
                        // 检查筛选器是否已经被添加到页面中
                        const isFilterVisible = fileSearch && fileSearch.offsetParent !== null;

                        // 根据筛选器的显示状态来控制 colorLegend
                        colorLegend.style.display = isFilterVisible ? 'none' : 'flex';
                    }
                }

                // 初始化时检查一次
                toggleColorLegend();

                // 监听 DOM 变化，检测筛选器是否被添加
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList') {
                            // 检查是否有新的节点被添加
                            mutation.addedNodes.forEach(function(node) {
                                if (node.nodeType === Node.ELEMENT_NODE) {
                                    // 检查是否包含 fileSearch 元素
                                    if (node.querySelector && node.querySelector('#fileSearch')) {
                                        toggleColorLegend();
                                    }
                                }
                            });
                        }
                    });
                });

                // 监听整个文档的变化
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });

                // 延迟检查，确保所有脚本都已执行
                setTimeout(toggleColorLegend, 100);
            };
        </script>
        <script src="${html.escape(details.sorter.js)}"></script>
        <script src="${html.escape(details.blockNavigation.js)}"></script>
        <script src="remove-blue.js"></script>
    </body>
</html>
    `
}

function detailTemplate(data) {
  const lineNumbers = new Array(data.maxLines).fill().map((_, i) => i + 1)
  const lineLink = num =>
    `<a name='L${num}'></a><a href='#L${num}'>${num}</a>`

  const lineCount = line => {
    // 优先显示新增语句，但只有被覆盖的才显示颜色
    if (line.addStatement === 'yes') {
      if (line.addStatementCovered === 'yes') {
        return `<span class="cline-any cline-yes" title="新增语句 - 已覆盖">新增语句</span>`
      } else if (line.addStatementCovered === 'partial') {
        return `<span class="cline-any cline-partial" title="新增语句 - 部分覆盖">新增语句</span>`
      } else {
        // 对于未覆盖或无数据的新增语句，不显示颜色
        return `<span class="cline-any cline-neutral" title="新增语句">新增语句</span>`
      }
    }

    // 根据覆盖状态显示
    if (line.covered === 'partial') {
      return `<span class="cline-any cline-partial" title="函数被调用但内部有未执行的代码">部分覆盖</span>`
    } else if (line.covered === 'yes') {
      const hitCount = line.hits !== '&nbsp;' ? ` (${line.hits})` : ''
      return `<span class="cline-any cline-yes" title="此行已被覆盖${hitCount}">已覆盖</span>`
    } else if (line.covered === 'no') {
      return `<span class="cline-any cline-no" title="此行未被覆盖">未覆盖</span>`
    } else {
      return ''
    }
  }

  // 生成带有覆盖率样式的表格行（不需要表头）
  const tableRows = []
  for (let i = 0; i < data.maxLines; i++) {
    const lineNum = i + 1
    const lineCoverage = data.lineCoverage[i]
    const covered = lineCoverage ? lineCoverage.covered : 'neutral'

    // 根据覆盖率状态添加 CSS 类
    let rowClass = ''
    if (covered === 'partial') {
      rowClass = 'covered-partial'
    } else if (covered === 'yes') {
      rowClass = 'covered-yes'
    } else if (covered === 'no') {
      rowClass = 'covered-no'
    } else {
      rowClass = 'covered-neutral'
    }

    tableRows.push(`<tr class="${rowClass}">
            <td class="line-count">${lineNumbers[i] ? `<a name='L${lineNum}'></a><a href='#L${lineNum}'>${lineNum}</a>` : ''}</td>
            <td class="line-coverage">${lineCoverage ? lineCount(lineCoverage) : ''}</td>
            <td class="text">${data.annotatedCode[i] || ''}</td>
        </tr>`)
  }

  return tableRows.join('\n')
}
const summaryTableHeader = [
  '<div class="code-container">',
  '<table class="coverage-summary">',
  '<thead>',
  '<tr style="background: linear-gradient(135deg, #454ce1 0%, #646CFF 100%); color: white;">',
  '   <th data-col="file" data-fmt="html" data-html="true" class="file" style="padding: 8px 12px; font-weight: 600;">📁 文件</th>',
  '   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic" style="padding: 8px 12px; font-weight: 600;">📊 图表</th>',
  '   <th data-col="statements" data-type="number" data-fmt="pct" class="pct" style="padding: 8px 12px; font-weight: 600;">📝 语句</th>',
  '   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs" style="padding: 8px 12px; font-weight: 600;">数量</th>',
  '   <th data-col="branches" data-type="number" data-fmt="pct" class="pct" style="padding: 8px 12px; font-weight: 600;">🌿 分支</th>',
  '   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs" style="padding: 8px 12px; font-weight: 600;">数量</th>',
  '   <th data-col="functions" data-type="number" data-fmt="pct" class="pct" style="padding: 8px 12px; font-weight: 600;">⚡ 函数</th>',
  '   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs" style="padding: 8px 12px; font-weight: 600;">数量</th>',
  '   <th data-col="lines" data-type="number" data-fmt="pct" class="pct" style="padding: 8px 12px; font-weight: 600;">📏 行数</th>',
  '   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs" style="padding: 8px 12px; font-weight: 600;">数量</th>',
  '   <th data-col="increment" data-type="number" data-fmt="pct" class="pct" style="padding: 8px 12px; font-weight: 600;">🆕 新增代码</th>',
  '   <th data-col="increment_raw" data-type="number" data-fmt="html" class="abs" style="padding: 8px 12px; font-weight: 600;">数量</th>',
  '</tr>',
  '</thead>',
  '<tbody>'
].join('\n')



// 判断是否是Vue组件级别的代码（与annotator.js中的函数保持一致）
function isVueComponentLevelCode(lineContent, filePath) {
  const trimmed = lineContent.trim()

  // 只处理Vue文件
  if (!filePath || !filePath.endsWith('.vue')) {
    return false
  }

  // 1. 所有import语句（模块加载时执行）
  if (trimmed.startsWith('import ')) {
    return true
  }

  // 2. 组件定义和配置（组件实例化时执行）
  if (trimmed.includes('export default') ||
    trimmed.includes('defineComponent') ||
    trimmed.match(/^\s*name\s*:/) ||
    trimmed.match(/^\s*components\s*:/) ||
    trimmed.match(/^\s*props\s*:/) ||
    trimmed.match(/^\s*emits\s*:/)) {
    return true
  }

  // 3. 组件注册（在components对象中的组件名）
  if (trimmed.match(/^[A-Z][a-zA-Z0-9]*\s*,?\s*$/) ||
    trimmed.match(/^[A-Z][a-zA-Z0-9]*\s*:\s*[A-Z][a-zA-Z0-9]*\s*,?\s*$/)) {
    return true
  }

  // 4. Vue 3 Composition API（setup时执行）
  if (trimmed.includes('defineProps') ||
    trimmed.includes('defineEmits') ||
    trimmed.includes('defineExpose') ||
    trimmed.includes('defineSlots')) {
    return true
  }

  // 5. 生命周期钩子定义（组件生命周期时自动调用）
  if (trimmed.match(/^\s*(created|mounted|beforeMount|beforeUpdate|updated|beforeUnmount|unmounted)\s*\(/)) {
    return true
  }

  // 6. Vue文件的结构标签
  if (trimmed.startsWith('<template') ||
    trimmed.startsWith('<style') ||
    trimmed.startsWith('<script')) {
    return true
  }

  return false
}

// 判断是否是模板或标签代码（与annotator.js中的函数保持一致）
function isTemplateOrTagCode(lineContent) {
  const trimmed = lineContent.trim()

  // HTML/XML 标签
  if (trimmed.startsWith('<') && (trimmed.endsWith('>') || trimmed.endsWith('/>'))) {
    return true
  }

  // Vue 模板语法
  if (trimmed.includes('v-if=') || trimmed.includes('v-for=') ||
    trimmed.includes('v-show=') || trimmed.includes('v-model=') ||
    trimmed.includes(':key=') || trimmed.includes('@click=') ||
    trimmed.includes('{{') || trimmed.includes('}}')) {
    return true
  }

  // React JSX 语法
  if (trimmed.includes('className=') || trimmed.includes('onClick=') ||
    trimmed.includes('onChange=') || trimmed.includes('onSubmit=')) {
    return true
  }

  // 模板字符串中的HTML
  if (trimmed.includes('`') && (trimmed.includes('<') || trimmed.includes('>'))) {
    return true
  }

  // CSS 样式
  if (trimmed.includes('style=') || trimmed.includes('class=')) {
    return true
  }

  return false
}

// 获取源代码文本（简化版本，实际可能需要更复杂的实现）
function getSourceText(filePath) {
  try {
    // 这里应该从源代码存储中获取文件内容
    // 暂时返回null，实际实现需要访问源代码
    return null
  } catch (error) {
    return null
  }
}

// 验证代码是否为真正的新增代码
function validateIncrementalCode(fileCoverage, context) {
  // 检查是否有新增代码标识
  if (!fileCoverage || !fileCoverage.data || !fileCoverage.data.lines) {
    return { isValid: false, incrementLines: [] }
  }

  const incrementLines = fileCoverage.data.lines || []

  // 如果没有新增行，直接返回无效
  if (incrementLines.length === 0) {
    return { isValid: false, incrementLines: [] }
  }

  // 验证新增行是否在合理范围内
  if (context && context.sourceFinder) {
    try {
      const sourceText = context.sourceFinder(fileCoverage.path)
      if (sourceText) {
        const totalLines = sourceText.split(/\r?\n/).length
        const validIncrementLines = incrementLines.filter(lineNum =>
          lineNum > 0 && lineNum <= totalLines
        )

        // 如果有效的新增行数量与原始数量不一致，说明数据可能有问题
        if (validIncrementLines.length !== incrementLines.length) {
          console.warn(`文件 ${fileCoverage.path} 的新增代码行号验证失败: 原始${incrementLines.length}行，有效${validIncrementLines.length}行`)
        }

        return {
          isValid: validIncrementLines.length > 0,
          incrementLines: validIncrementLines
        }
      }
    } catch (e) {
      // 如果获取源代码失败，继续使用原始数据
    }
  }

  // 如果无法获取源代码，但有新增行标识，认为是有效的
  return { isValid: true, incrementLines }
}

function summaryLineTemplate(details) {
  const { reportClasses, metrics, file, output } = details

  // 计算新增代码覆盖率
  const calculateIncrementCoverage = () => {
    // 优先使用传递过来的汇总数据
    if (details.incrementCoverage) {
      return details.incrementCoverage
    }

    // 验证新增代码的有效性
    const validation = validateIncrementalCode(details.fileCoverage, details.context)
    if (!validation.isValid) {
      return { pct: 0, covered: 0, total: 0 }
    }

    const incrementLines = validation.incrementLines

    // 检查文件的整体覆盖率，如果为0，则新增代码覆盖率也为0
    const summary = details.fileCoverage.toSummary()
    if (summary.branches.total > 0 && summary.branches.covered === 0) {
      return {
        pct: 0,
        covered: 0,
        total: incrementLines.length
      }
    }
    // 使用分支覆盖率数据而不是行覆盖率数据，与分支覆盖率保持一致
    const branchStats = details.fileCoverage.b
    const branchMeta = details.fileCoverage.branchMap

    let totalIncrementBranches = 0
    let coveredIncrementBranches = 0

    // 获取源代码来检查是否是标签代码（使用与annotator相同的方式）
    const fs = require('fs')
    let sourceLines = []
    try {
      if (details.fileCoverage.path) {
        const sourceText = fs.readFileSync(details.fileCoverage.path, 'utf8')
        sourceLines = sourceText.split(/\r?\n/)
      }
    } catch (e) {
      // Cannot read source file
    }

    // 遍历所有分支，找出在新增行范围内的分支
    if (branchStats && branchMeta) {
      Object.entries(branchStats).forEach(([branchName, branchArray]) => {
        const meta = branchMeta[branchName]
        if (!meta || !meta.locations) {
          return
        }

        // 检查分支是否在新增行范围内
        const branchInIncrementLines = meta.locations.some(location => {
          if (!location.start) return false
          const branchLine = location.start.line
          return incrementLines.includes(branchLine)
        })

        if (branchInIncrementLines) {
          // 计算这个分支的覆盖情况（与分支覆盖率计算逻辑一致）
          branchArray.forEach(hits => {
            totalIncrementBranches++
            if (hits > 0) {
              coveredIncrementBranches++
            }
          })
        }
      })
    }

    // 如果没有找到任何分支，使用语句覆盖率数据进行计算（与分支覆盖率逻辑保持一致）
    if (totalIncrementBranches === 0) {
      const statementStats = details.fileCoverage.s
      const statementMeta = details.fileCoverage.statementMap

      if (statementStats && statementMeta) {
        Object.entries(statementStats).forEach(([stName, count]) => {
          const meta = statementMeta[stName]
          if (meta.skip) {
            return
          }

          // 检查语句是否在新增行范围内
          const statementStartLine = meta.start.line
          const statementEndLine = meta.end.line

          // 判断语句是否与新增行有交集
          const isInIncrementLines = incrementLines.some(lineNum =>
            lineNum >= statementStartLine && lineNum <= statementEndLine
          )

          if (isInIncrementLines) {
            totalIncrementBranches++

            if (count > 0) {
              // 语句被执行，计为已覆盖
              coveredIncrementBranches++
            } else {
              // 语句未被执行，检查是否是标签代码或Vue组件代码
              const lineContent = sourceLines[statementStartLine - 1] || ''
              const isTag = isTemplateOrTagCode(lineContent.trim())
              const isVueComponent = isVueComponentLevelCode(lineContent.trim(), details.fileCoverage.path)

              if (isTag || isVueComponent) {
                // 标签代码或Vue组件代码视为已覆盖
                coveredIncrementBranches++
              }
            }
          }
        })
      }

      // 如果仍然没有找到任何数据，最后回退到行级别计算
      if (totalIncrementBranches === 0) {
        incrementLines.forEach(lineNum => {
          totalIncrementBranches++
          const lineContent = sourceLines[lineNum - 1] || ''
          const isTag = isTemplateOrTagCode(lineContent.trim())
          const isVueComponent = isVueComponentLevelCode(lineContent.trim(), details.fileCoverage.path)

          if (isTag || isVueComponent) {
            // 标签代码或Vue组件代码视为已覆盖
            coveredIncrementBranches++
          }
        })
      }
    }

    const pct = totalIncrementBranches > 0 ? Math.round((coveredIncrementBranches / totalIncrementBranches) * 100) : 0

    return {
      pct: pct,
      covered: coveredIncrementBranches,
      total: totalIncrementBranches
    }
  }

  const incrementMetrics = calculateIncrementCoverage()

  // 获取新增代码覆盖率的颜色类
  const getIncrementColorClass = (pct) => {
    if (pct >= 80) return 'high'
    if (pct >= 60) return 'medium'
    return 'low'
  }
  const percentGraph = pct => {
    if (!isFinite(pct)) {
      return ''
    }

    const cls = ['cover-fill']
    if (pct === 100) {
      cls.push('cover-full')
    }

    pct = Math.floor(pct)
    return [
      `<div class="${cls.join(' ')}" style="width: ${pct}%"></div>`,
      `<div class="cover-empty" style="width: ${100 - pct}%"></div>`
    ].join('')
  }
  const summaryType = (type, showGraph = false) => {
    const info = metrics[type]
    const reportClass = reportClasses[type]
    const result = [
      `<td data-value="${info.pct}" class="pct ${reportClass}">${info.pct}%</td>`,
      `<td data-value="${info.total}" class="abs ${reportClass}">${info.covered}/${info.total}</td>`
    ]
    if (showGraph) {
      result.unshift(
        `<td data-value="${info.pct}" class="pic ${reportClass}">`,
        `<div class="chart">${percentGraph(info.pct)}</div>`,
        `</td>`
      )
    }

    return result
  }

  return []
    .concat(
      '<tr>',
      `<td class="file ${reportClasses.statements
      }" data-value="${html.escape(file)}"><a href="${html.escape(
        output
      )}">${html.escape(file)}</a></td>`,
      summaryType('statements', true),
      summaryType('branches'),
      summaryType('functions'),
      summaryType('lines'),
      // 新增代码覆盖率列
      `<td data-value="${incrementMetrics.pct}" class="pct ${getIncrementColorClass(incrementMetrics.pct)}">${incrementMetrics.pct}%</td>`,
      `<td data-value="${incrementMetrics.total}" class="abs ${getIncrementColorClass(incrementMetrics.pct)}">${incrementMetrics.covered}/${incrementMetrics.total}</td>`,
      '</tr>\n'
    )
    .join('\n\t')
}

const summaryTableFooter = ['</tbody>', '</table>', '</div>'].join('\n')
const emptyClasses = {
  statements: 'empty',
  lines: 'empty',
  functions: 'empty',
  branches: 'empty'
}

const standardLinkMapper = {
  getPath(node) {
    if (typeof node === 'string') {
      return node
    }
    let filePath = node.getQualifiedName()
    if (node.isSummary()) {
      if (filePath !== '') {
        filePath += '/index.html'
      } else {
        filePath = 'index.html'
      }
    } else {
      filePath += '.html'
    }
    return filePath
  },

  relativePath(source, target) {
    const targetPath = this.getPath(target)
    const sourcePath = path.dirname(this.getPath(source))
    return path.posix.relative(sourcePath, targetPath)
  },

  assetPath(node, name) {
    return this.relativePath(this.getPath(node), name)
  }
}

function fixPct(metrics) {
  Object.keys(emptyClasses).forEach(key => {
    metrics[key].pct = 0
  })
  return metrics
}

class HtmlReport extends ReportBase {
  constructor(opts) {
    super()

    this.verbose = opts.verbose
    this.linkMapper = opts.linkMapper || standardLinkMapper
    this.subdir = opts.subdir || ''
    this.date = new Date().toISOString()
    this.skipEmpty = opts.skipEmpty
  }

  getBreadcrumbHtml(node) {
    let parent = node.getParent()
    const nodePath = []

    while (parent) {
      nodePath.push(parent)
      parent = parent.getParent()
    }

    const linkPath = nodePath.map(ancestor => {
      const target = this.linkMapper.relativePath(node, ancestor)
      const name = ancestor.getRelativeName() || '所有文件'
      return '<a href="' + target + '">' + name + '</a>'
    })

    linkPath.reverse()
    return linkPath.length > 0
      ? linkPath.join(' / ') + ' ' + node.getRelativeName()
      : '所有文件'
  }

  fillTemplate(node, templateData, context) {
    const linkMapper = this.linkMapper
    const summary = node.getCoverageSummary()
    templateData.entity = node.getQualifiedName() || '所有文件'
    templateData.metrics = summary
    templateData.reportClass = context.classForPercent(
      'statements',
      summary.statements.pct
    )
    templateData.pathHtml = this.getBreadcrumbHtml(node)
    templateData.base = {
      css: linkMapper.assetPath(node, 'base.css')
    }
    templateData.sorter = {
      js: linkMapper.assetPath(node, 'sorter.js'),
      image: linkMapper.assetPath(node, 'sort-arrow-sprite.png')
    }
    templateData.blockNavigation = {
      js: linkMapper.assetPath(node, 'block-navigation.js')
    }
    templateData.prettify = {
      js: linkMapper.assetPath(node, 'prettify.js'),
      css: linkMapper.assetPath(node, 'prettify.css')
    }
    templateData.favicon = linkMapper.assetPath(node, 'favicon.png')

    // 计算新增代码覆盖率
    templateData.incrementMetrics = this.calculateNodeIncrementCoverage(node)
  }

  // 计算节点的新增代码覆盖率
  calculateNodeIncrementCoverage(node) {
    if (node.isSummary()) {
      // 如果是汇总节点（文件夹），使用已有的文件夹级别计算方法
      return this.calculateFolderIncrementCoverage(node)
    } else {
      // 如果是文件节点，计算单个文件的新增代码覆盖率
      const fileCoverage = node.getFileCoverage()

      // 验证新增代码的有效性
      const validation = validateIncrementalCode(fileCoverage, null)
      if (!validation.isValid) {
        return { pct: 0, covered: 0, total: 0 }
      }

      const incrementLines = validation.incrementLines

      // 检查文件的整体覆盖率，如果为0，则新增代码覆盖率也为0
      const summary = fileCoverage.toSummary()
      if (summary.branches.total > 0 && summary.branches.covered === 0) {
        return {
          pct: 0,
          covered: 0,
          total: incrementLines.length
        }
      }

      // 使用分支覆盖率数据而不是行覆盖率数据，与分支覆盖率保持一致
      const branchStats = fileCoverage.b
      const branchMeta = fileCoverage.branchMap

      let totalIncrementBranches = 0
      let coveredIncrementBranches = 0

      // 获取源代码来检查是否是标签代码（使用与annotator相同的方式）
      const fs = require('fs')
      let sourceLines = []
      try {
        if (fileCoverage.path) {
          const sourceText = fs.readFileSync(fileCoverage.path, 'utf8')
          sourceLines = sourceText.split(/\r?\n/)
        }
      } catch (e) {
        // Cannot read source file
      }

      // 遍历所有分支，找出在新增行范围内的分支
      if (branchStats && branchMeta) {
        Object.entries(branchStats).forEach(([branchName, branchArray]) => {
          const meta = branchMeta[branchName]
          if (!meta || !meta.locations) {
            return
          }

          // 检查分支是否在新增行范围内
          const branchInIncrementLines = meta.locations.some(location => {
            if (!location.start) return false
            const branchLine = location.start.line
            return incrementLines.includes(branchLine)
          })

          if (branchInIncrementLines) {
            // 计算这个分支的覆盖情况（与分支覆盖率计算逻辑一致）
            branchArray.forEach(hits => {
              totalIncrementBranches++
              if (hits > 0) {
                coveredIncrementBranches++
              }
            })
          }
        })
      }

      // 如果没有找到任何分支，使用语句覆盖率数据进行计算（与分支覆盖率逻辑保持一致）
      if (totalIncrementBranches === 0) {
        const statementStats = fileCoverage.s
        const statementMeta = fileCoverage.statementMap

        if (statementStats && statementMeta) {
          Object.entries(statementStats).forEach(([stName, count]) => {
            const meta = statementMeta[stName]
            if (meta.skip) {
              return
            }

            // 检查语句是否在新增行范围内
            const statementStartLine = meta.start.line
            const statementEndLine = meta.end.line

            // 判断语句是否与新增行有交集
            const isInIncrementLines = incrementLines.some(lineNum =>
              lineNum >= statementStartLine && lineNum <= statementEndLine
            )

            if (isInIncrementLines) {
              totalIncrementBranches++

              if (count > 0) {
                // 语句被执行，计为已覆盖
                coveredIncrementBranches++
              } else {
                // 语句未被执行，检查是否是标签代码或Vue组件代码
                const lineContent = sourceLines[statementStartLine - 1] || ''
                const isTag = isTemplateOrTagCode(lineContent.trim())
                const isVueComponent = isVueComponentLevelCode(lineContent.trim(), fileCoverage.path)

                if (isTag || isVueComponent) {
                  // 标签代码或Vue组件代码视为已覆盖
                  coveredIncrementBranches++
                }
              }
            }
          })
        }

        // 如果仍然没有找到任何数据，最后回退到行级别计算
        if (totalIncrementBranches === 0) {
          incrementLines.forEach(lineNum => {
            totalIncrementBranches++
            const lineContent = sourceLines[lineNum - 1] || ''
            const isTag = isTemplateOrTagCode(lineContent.trim())
            const isVueComponent = isVueComponentLevelCode(lineContent.trim(), fileCoverage.path)

            if (isTag || isVueComponent) {
              // 标签代码或Vue组件代码视为已覆盖
              coveredIncrementBranches++
            }
          })
        }
      }

      const pct = totalIncrementBranches > 0 ? Math.round((coveredIncrementBranches / totalIncrementBranches) * 100) : 0

      return {
        pct: pct,
        covered: coveredIncrementBranches,
        total: totalIncrementBranches
      }
    }
  }

  getTemplateData() {
    return { datetime: this.date }
  }

  getWriter(context) {
    if (!this.subdir) {
      return context.writer
    }
    return context.writer.writerForDir(this.subdir)
  }

  onStart(root, context) {
    const assetHeaders = {
      '.js': '/* eslint-disable */\n'
    };

    ['.', 'vendor'].forEach(subdir => {
      const writer = this.getWriter(context)
      const srcDir = path.resolve(__dirname, 'assets', subdir)
      fs.readdirSync(srcDir).forEach(f => {
        const resolvedSource = path.resolve(srcDir, f)
        const resolvedDestination = '.'
        const stat = fs.statSync(resolvedSource)
        let dest

        if (stat.isFile()) {
          dest = resolvedDestination + '/' + f
          if (this.verbose) {
            // Write asset: dest
          }
          writer.copyFile(
            resolvedSource,
            dest,
            assetHeaders[path.extname(f)]
          )
        }
      })
    })
  }

  // 递归计算文件夹级别的新增代码覆盖率 - 使用分支覆盖率逻辑
  calculateFolderIncrementCoverage(node) {
    let totalIncrementBranches = 0
    let coveredIncrementBranches = 0

    const processNode = (currentNode) => {
      if (currentNode.isSummary()) {
        // 如果是文件夹，递归处理子节点
        currentNode.getChildren().forEach(child => {
          processNode(child)
        })
      } else {
        // 如果是文件，计算其新增代码覆盖率
        const fileCoverage = currentNode.getFileCoverage()

        // 验证新增代码的有效性
        const validation = validateIncrementalCode(fileCoverage, null)
        if (!validation.isValid) {
          return // 跳过无效的新增代码
        }

        const incrementLines = validation.incrementLines
        // 使用分支覆盖率数据而不是行覆盖率数据，与分支覆盖率保持一致
        const branchStats = fileCoverage.b
        const branchMeta = fileCoverage.branchMap

        // 检查文件的整体覆盖率，如果为0，则新增代码覆盖率也为0
        const summary = fileCoverage.toSummary()
        if (summary.branches.total > 0 && summary.branches.covered === 0) {
          // 文件整体分支覆盖率为0，新增代码也不计为覆盖
          totalIncrementBranches += incrementLines.length
          // coveredIncrementBranches 不增加，保持为0
          return
        }

        // 获取源代码来检查是否是标签代码（使用与annotator相同的方式）
        const fs = require('fs')
        let sourceLines = []
        try {
          const sourceText = fs.readFileSync(fileCoverage.path, 'utf8')
          sourceLines = sourceText.split(/\r?\n/)
        } catch (e) {
          // Cannot read source file
        }

        // 遍历所有分支，找出在新增行范围内的分支
        let fileTotalBranches = 0
        let fileCoveredBranches = 0

        if (branchStats && branchMeta) {
          Object.entries(branchStats).forEach(([branchName, branchArray]) => {
            const meta = branchMeta[branchName]
            if (!meta || !meta.locations) {
              return
            }

            // 检查分支是否在新增行范围内
            const branchInIncrementLines = meta.locations.some(location => {
              if (!location.start) return false
              const branchLine = location.start.line
              return incrementLines.includes(branchLine)
            })

            if (branchInIncrementLines) {
              // 计算这个分支的覆盖情况（与分支覆盖率计算逻辑一致）
              branchArray.forEach(hits => {
                fileTotalBranches++
                if (hits > 0) {
                  fileCoveredBranches++
                }
              })
            }
          })
        }

        // 如果没有找到任何分支，使用语句覆盖率数据进行计算（与分支覆盖率逻辑保持一致）
        if (fileTotalBranches === 0) {
          const statementStats = fileCoverage.s
          const statementMeta = fileCoverage.statementMap

          if (statementStats && statementMeta) {
            Object.entries(statementStats).forEach(([stName, count]) => {
              const meta = statementMeta[stName]
              if (meta.skip) {
                return
              }

              // 检查语句是否在新增行范围内
              const statementStartLine = meta.start.line
              const statementEndLine = meta.end.line

              // 判断语句是否与新增行有交集
              const isInIncrementLines = incrementLines.some(lineNum =>
                lineNum >= statementStartLine && lineNum <= statementEndLine
              )

              if (isInIncrementLines) {
                fileTotalBranches++

                if (count > 0) {
                  // 语句被执行，计为已覆盖
                  fileCoveredBranches++
                } else {
                  // 语句未被执行，检查是否是标签代码或Vue组件代码
                  const lineContent = sourceLines[statementStartLine - 1] || ''
                  const isTag = isTemplateOrTagCode(lineContent.trim())
                  const isVueComponent = isVueComponentLevelCode(lineContent.trim(), fileCoverage.path)

                  if (isTag || isVueComponent) {
                    // 标签代码或Vue组件代码视为已覆盖
                    fileCoveredBranches++
                  }
                }
              }
            })
          }

          // 如果仍然没有找到任何数据，最后回退到行级别计算
          if (fileTotalBranches === 0) {
            incrementLines.forEach(lineNum => {
              fileTotalBranches++
              const lineContent = sourceLines[lineNum - 1] || ''
              const isTag = isTemplateOrTagCode(lineContent.trim())
              const isVueComponent = isVueComponentLevelCode(lineContent.trim(), fileCoverage.path)

              if (isTag || isVueComponent) {
                // 标签代码或Vue组件代码视为已覆盖
                fileCoveredBranches++
              }
            })
          }
        }

        totalIncrementBranches += fileTotalBranches
        coveredIncrementBranches += fileCoveredBranches
      }
    }

    processNode(node)

    const pct = totalIncrementBranches > 0 ? Math.round((coveredIncrementBranches / totalIncrementBranches) * 100) : 0

    return {
      pct: pct,
      covered: coveredIncrementBranches,
      total: totalIncrementBranches
    }
  }

  onSummary(node, context) {
    const linkMapper = this.linkMapper
    const templateData = this.getTemplateData()
    const children = node.getChildren()
    const skipEmpty = this.skipEmpty

    this.fillTemplate(node, templateData, context)
    const cw = this.getWriter(context).writeFile(linkMapper.getPath(node))
    cw.write(headerTemplate(templateData))
    cw.write(summaryTableHeader)
    children.forEach(child => {
      const metrics = child.getCoverageSummary()
      const isEmpty = metrics.isEmpty()
      if (skipEmpty && isEmpty) {
        return
      }
      const reportClasses = isEmpty
        ? emptyClasses
        : {
          statements: context.classForPercent(
            'statements',
            metrics.statements.pct
          ),
          lines: context.classForPercent(
            'lines',
            metrics.lines.pct
          ),
          functions: context.classForPercent(
            'functions',
            metrics.functions.pct
          ),
          branches: context.classForPercent(
            'branches',
            metrics.branches.pct
          )
        }
      const data = {
        metrics: isEmpty ? fixPct(metrics) : metrics,
        reportClasses,
        file: child.getRelativeName(),
        output: linkMapper.relativePath(node, child),
        fileCoverage: child.getFileCoverage ? child.getFileCoverage() : null,
        incrementCoverage: this.calculateFolderIncrementCoverage(child),
        context: context
      }
      cw.write(summaryLineTemplate(data) + '\n')
    })
    cw.write(summaryTableFooter)
    cw.write(footerTemplate(templateData))
    cw.close()
  }

  onDetail(node, context) {
    const linkMapper = this.linkMapper
    const templateData = this.getTemplateData()

    this.fillTemplate(node, templateData, context)
    const cw = this.getWriter(context).writeFile(linkMapper.getPath(node))
    cw.write(headerTemplate(templateData))
    cw.write('<div class="code-container">\n')
    cw.write('<table class="coverage">\n')
    cw.write(detailTemplate(annotator(node.getFileCoverage(), context)))
    cw.write('</table>\n')
    cw.write('</div>\n')
    cw.write(footerTemplate(templateData))
    cw.close()
  }
}

module.exports = HtmlReport
