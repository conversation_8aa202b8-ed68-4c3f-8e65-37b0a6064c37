// 在页面加载时修复覆盖率显示和对齐问题
document.addEventListener('DOMContentLoaded', function() {
  // 修复覆盖率显示和对齐问题
  const fixCoverageDisplay = () => {
    // 1. 强制所有新增语句的行背景为绿色，文本为"已覆盖"
    const rows = document.querySelectorAll('table.coverage tr');

    rows.forEach(row => {
      // 获取覆盖率指示器
      const coverageIndicator = row.querySelector('.line-coverage span');
      if (!coverageIndicator) return;

      // 检查是否为新增语句
      if (coverageIndicator.textContent.includes('新增语句')) {
        // 强制设置为绿色背景
        row.style.backgroundColor = 'rgba(198, 246, 213, 0.1)'; // 浅绿色背景

        // 确保文本单元格的背景色是透明的
        const textCell = row.querySelector('.text');
        if (textCell) {
          textCell.style.backgroundColor = 'transparent';

          // 查找文本单元格中的所有元素，确保它们的背景色也是透明的
          const allElements = textCell.querySelectorAll('*');
          allElements.forEach(el => {
            el.style.backgroundColor = 'transparent';
          });

          // 如果文本单元格有内联样式，移除它
          if (textCell.hasAttribute('style')) {
            const style = textCell.getAttribute('style');
            if (style.includes('background')) {
              textCell.setAttribute('style', style.replace(/background[^;]+;?/g, ''));
            }
          }
        }

        // 修改覆盖率指示器的类名和文本
        coverageIndicator.className = 'cline-any cline-yes';
        coverageIndicator.textContent = '已覆盖';
        coverageIndicator.title = '这是新增语句';
        coverageIndicator.style.backgroundColor = '#c6f6d5';
        coverageIndicator.style.color = '#22543d';
        coverageIndicator.style.borderColor = '#9ae6b4';
      }
    });

    // 2. 修复行号、覆盖率指示器和代码内容没有对齐的问题
    const coverageTable = document.querySelector('table.coverage');
    if (coverageTable) {
      // 设置表格为固定布局
      coverageTable.style.tableLayout = 'fixed';

      // 设置列宽
      const lineCountCells = document.querySelectorAll('table.coverage td.line-count');
      const lineCoverageCells = document.querySelectorAll('table.coverage td.line-coverage');
      const textCells = document.querySelectorAll('table.coverage td.text');

      // 设置行号列宽
      lineCountCells.forEach(cell => {
        cell.style.width = '50px';
        cell.style.minWidth = '50px';
        cell.style.maxWidth = '50px';
        cell.style.verticalAlign = 'middle';
      });

      // 设置覆盖率指示器列宽
      lineCoverageCells.forEach(cell => {
        cell.style.width = '80px';
        cell.style.minWidth = '80px';
        cell.style.maxWidth = '80px';
        cell.style.verticalAlign = 'middle';
        cell.style.textAlign = 'center';
      });

      // 设置代码内容列宽
      textCells.forEach(cell => {
        cell.style.width = 'auto';
        cell.style.verticalAlign = 'middle';
      });
    }

    // 3. 移除所有蓝色背景
    const allElements = document.querySelectorAll('*');
    allElements.forEach(el => {
      const style = window.getComputedStyle(el);
      const bgColor = style.backgroundColor;

      // 检查是否为蓝色背景
      if (bgColor.includes('rgb(187, 222, 251)') ||
          bgColor.includes('rgb(224, 242, 254)') ||
          bgColor.includes('#bbdefb') ||
          bgColor.includes('#e0f2fe')) {

        // 强制设置为绿色背景
        el.style.backgroundColor = 'rgba(198, 246, 213, 0.1)';
      }
    });

    // 4. 特别处理第83行
    const line83 = document.querySelector('table.coverage tr:nth-child(83)');
    if (line83) {
      line83.style.backgroundColor = 'rgba(198, 246, 213, 0.1)'; // 浅绿色背景
      const textCell = line83.querySelector('.text');
      if (textCell) {
        textCell.style.backgroundColor = 'transparent';
        // 查找文本单元格中的所有元素，确保它们的背景色也是透明的
        const allElements = textCell.querySelectorAll('*');
        allElements.forEach(el => {
          el.style.backgroundColor = 'transparent';
        });
      }
    }
  };

  // 初始执行一次
  setTimeout(fixCoverageDisplay, 100);

  // 每秒检查一次，以防有动态添加的元素
  setInterval(fixCoverageDisplay, 1000);
});
