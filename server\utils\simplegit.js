// 代码增量分析
const axios = require('axios');
const { parsePatch } = require("diff"); // 引入 diff 库的 parsePatc

// 运维平台api地址
const ylopsIp = 'https://ylops.jtexpress.com.cn'
// 运维平台token
const ylopsToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoyMDU2MjA0Nzk5LCJqdGkiOiIwYzk0NGU0YjFjZjc0YmM2YjNiYmNhOWQzMWY5YTRmYiIsInVzZXJfaWQiOjN9.1N5RbG4_U-YD5QYxXKeUtOGKKXN2ITiYx4-nCZPpvKs';

// gitlab 配置

// 项目ID
const gitlabIp = 'http://code.jms.com/api/v4/projects'
// 最新commitId
let latestCommitId = '';
// 当前生产commitId
let productionCommitId = '';

// 根据commit节点获取更新代码
const getUpdateCode = function (appId, token) {
  return new Promise(async (resolve, reject) => {
    try {
      // 增加 try-catch 处理异步错误
      const res = await axios.get(`${gitlabIp}/${appId}/repository/compare?from=${productionCommitId}&to=${latestCommitId}`, { headers: { "PRIVATE-TOKEN": token } });
      const diffs = res.data.diffs;

      // 获取对应更改的文件路径及 *新增或修改的行号*
      const updateCode = diffs.map((diff) => {
        const changedLines = []; // 存储新增或修改的行号（在新文件中的行号）
        if (diff.diff) {
          // 确保 diff 内容存在
          try {
            // 增加 try-catch 处理解析错误
            const patches = parsePatch(diff.diff); // 解析 diff
            if (patches && patches.length > 0) {
              patches[0].hunks.forEach((hunk) => {
                let currentNewLineNumber = hunk.newStart; // 从 hunk 的新起始行号开始计数
                hunk.lines.forEach((line) => {
                  const linePrefix = line.charAt(0);
                  if (linePrefix === "+") {
                    // 如果是新增行
                    changedLines.push(currentNewLineNumber); // 记录当前新行号
                    currentNewLineNumber++; // 新行号加 1
                  } else if (linePrefix === " ") {
                    // 如果是未改变的上下文行
                    currentNewLineNumber++; // 新行号加 1
                  }
                  // 如果是删除行 ('-')，它在新文件中不存在，所以忽略，新行号不增加
                });
              });
            }
          } catch (parseError) {
            console.error(`解析 diff 出错 (${diff.new_path}):`, parseError);
            // 可以选择记录错误或返回空数组等
          }
        }
        return {
          path: diff.new_path,
          lines: changedLines, // 返回包含新增/修改行号的数组
        };
      });
      resolve(updateCode);
    } catch (error) {
      reject(error); // 发生错误时 reject Promise
    }
  });
};

// 遍历json数据，获取更新代码
/**
 * 遍历覆盖率JSON数据，处理新增代码的覆盖率信息
 * @param {Object} coverageJson 原始覆盖率数据
 * @param {boolean} isIncrement 是否只返回增量覆盖率数据
 * @param {string} appId 项目ID
 * @param {string} token GitLab访问令牌
 * @returns {Promise<Object>} 处理后的覆盖率数据
 */
const traverseJson = function ({ coverageJson, isIncrement = false, appId, token }) {
  try {
    return new Promise(async (resolve, reject) => {
      // 获取代码变更信息
      const updateCodeList = await getUpdateCode(appId, token) || [];
      // 创建原始数据的副本
      const coverageData = { ...coverageJson };
      // 获取所有文件路径
      const filePaths = Object.keys(coverageData);

      // 根据模式创建结果对象
      const filteredCoverageData = isIncrement ? {} : { ...coverageData };

      // 创建文件路径映射，提高查找效率
      const filePathMap = new Map();
      filePaths.forEach((filePath) => {
        const normalizedFilePath = filePath.replace(/\\/g, "/");
        filePathMap.set(normalizedFilePath, filePath);
      });

      // 统计处理的文件数
      let processedFiles = 0;

      // 处理每个更新代码项
      updateCodeList.forEach((item) => {
        if (!item.path || !item.lines || item.lines.length === 0) return;

        // 标准化路径
        const normalizedPath = item.path.replace(/\\/g, "/");

        // 查找匹配的文件路径
        let findFilePath = null;
        for (const [normalizedFilePath, originalPath] of filePathMap.entries()) {
          if (normalizedFilePath.endsWith(normalizedPath) ||
            normalizedFilePath.includes(`/${normalizedPath}`)) {
            findFilePath = originalPath;
            break;
          }
        }

        if (!findFilePath) return; // 如果没找到匹配的文件，跳过

        processedFiles++;

        // 更新文件数据
        filteredCoverageData[findFilePath] = {
          ...coverageData[findFilePath],
          lines: item.lines
        };
      });

      // 输出处理统计信息
      console.log(`处理完成: ${processedFiles}个文件中有${updateCodeList.length}个文件包含新增语句`);

      resolve(filteredCoverageData); // 返回处理后的覆盖率数据
    });
  } catch (error) {
    console.error("处理覆盖率数据时出错:", error);
    // 即使出错也返回原始数据，避免整个流程中断
    return coverageJson;
  }
};


/** 根据项目名称、应用名称、产品名称回去对应的镜像版本号
 * @param {*} projectName 项目名称uat
 * @param {*} applicationName 应用名称 jmscb-servicequality-web-front
 * @param {*} productName 产品名称 jms
 */
const getImageVersion = function ({
  projectName,
  applicationName,
  productName,
}) {
  return new Promise(async (resolve, reject) => {
    try {
      let setProductName = productName?.includes('jms') ? productName : 'jms' + productName;
      const { data } = await axios.get(
        `${ylopsIp}/api/open/image_version?app_name=${applicationName}&product_name=${setProductName}&project_name=${projectName}`,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${ylopsToken}`,
          }
        }
      );
      // 解析数据获取commit节点
      const pro = data.data.find((item) => item.environment === 2);
      // 343_20221017140633_abbaeb95 获取最后下划线后面的字符串
      productionCommitId = pro?.version.split("_")[2];
      resolve(data.data);
    } catch (error) {
      reject(error);
    }
  })
}

/**
 * 根据项目名称获取生产分支最新的commit节点
 * @param {*} applicationName 项目名称
 */
const getProductionCommit = function ({ appId, token }) {
  return new Promise(async (resolve, reject) => {
    try {
      ;
      const apiUrl = `${gitlabIp}/${appId}/repository/commits?ref_name=production&per_page=1&page=1`;
      const { data } = await axios.get(
        apiUrl,
        {
          headers: { "PRIVATE-TOKEN": token }
        }
      );
      if (data && data.length > 0) {
        latestCommitId = data[0].short_id
        resolve(data[0]);
      } else {
        resolve(null);
      }
    } catch (error) {
      console.error("获取最新的commit节点时出错:", error);
      reject(error);
    }
  })
}

module.exports = {
  getImageVersion,
  traverseJson,
  getProductionCommit,
  gitlabIp
};
