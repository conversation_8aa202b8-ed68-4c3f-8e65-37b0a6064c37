const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const app = express();
const port = process.env.PORT || 443;
const https = require('https');

app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, '../views')); // 设置视图文件夹路径

// 将环境变量传递给所有 EJS 模板
app.locals.APP_IP = process.env.APP_IP;
const expressLayouts = require('express-ejs-layouts');
app.use(expressLayouts);

// 中间件配置
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(express.static(path.join(__dirname, '../public')));
app.use('/coverage', express.static(path.join(__dirname, '../coverage')));

// 接口路由
const requestRoutes = require('./controllers/requestRoutes');
app.use('/api', requestRoutes);
// 页面路由
const pageRoutes = require('./controllers/pageRoutes');
app.use(pageRoutes);

// 确保覆盖率报告目录存在
const coverageDir = path.join(__dirname, '../coverage');
if (!fs.existsSync(coverageDir)) {
    fs.mkdirSync(coverageDir, { recursive: true });
}

const options = {
    key: fs.readFileSync('key.pem'),
    cert: fs.readFileSync('cert.pem')
};
https.createServer(options, app).listen(port, '0.0.0.0', () => {
    console.log(`HTTPS 服务器已启动，端口号：${port}`);
});

// 启动服务器
// app.listen(port, '0.0.0.0', () => {
//     console.log(`启动成功端口号：${port}`);
// });
