// 删除任务
const { clearCatalogue } = require('./tools');
const deleteTask =  function({ country,applicationName,ids }){
  return new Promise(async(resolve, reject)=>{
    try{
        if(ids.length){
            ids.forEach(async(id)=>{
                let coverage = `./coverage/${country}/${applicationName}/${id}`
                let json = `./json/${country}/${applicationName}/${id}`
                console.log("----->deleteTask 开始删除任务")
                await clearCatalogue(coverage)
                await clearCatalogue(json)
                console.log("----->deleteTask 删除任务成功")
            })
            resolve({ code: 1, msg: '删除成功', data: true });
        }else{
            resolve({ code: 401, msg: '删除失败', data: '请传入id' });
        }
    }catch(error){
        resolve({ code: 401, msg: '删除失败', data: false });
    }
  })
}

module.exports = {
    deleteTask
};
