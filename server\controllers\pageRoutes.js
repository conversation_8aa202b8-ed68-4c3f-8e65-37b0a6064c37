const express = require('express');
const router = express.Router();

router.get('/layout', (req, res) => {
    res.render('pages/home', {
        title: '代码覆盖率汇总',
        activePage: 'home'
    });
});

// 关于页面路由
// router.get('/about', (req, res) => {
//     const country = req.query.country;
//     res.render('pages/about', {
//         title: '代码覆盖率明细',
//         activePage: 'about',
//         country
//     });
// });


// 404 处理
router.use((req, res) => {
    res.status(404).render('pages/404', {
        title: '404 Not Found',
        activePage: '',
    });
});

module.exports = router;
