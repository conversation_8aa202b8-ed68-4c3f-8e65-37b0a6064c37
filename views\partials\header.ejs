<style>
  .nav {
    display: flex;
    justify-content: space-between;
  }
</style>
<header>
  <nav class="nav">
    <ul>
      <li class="<%= activePage === 'home' ? 'active' : '' %>">
        <a href="<%= `/layout` %>">汇总</a>
      </li>
    </ul>
    <div class="header-tips">国家：<span class="country-name"></span></div>
  </nav>
</header>
<script>
  const countryNameDom = document.querySelector(".country-name");
  var url1 = new URL(window.location.href);
  var country1 = url1.searchParams.get("country");
  countryNameDom.innerHTML = country1;
</script>
