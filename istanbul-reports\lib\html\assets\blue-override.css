/* 覆盖所有蓝色背景 */
tr[style*="background-color: rgb(187, 222, 251)"],
tr[style*="background-color: #bbdefb"],
tr[style*="background-color: rgba(187, 222, 251"],
tr[style*="background: rgb(187, 222, 251)"],
tr[style*="background: #bbdefb"],
tr[style*="background: rgba(187, 222, 251"],
tr[style*="background-color: rgb(224, 242, 254)"],
tr[style*="background-color: #e0f2fe"],
tr[style*="background-color: rgba(224, 242, 254"],
tr[style*="background: rgb(224, 242, 254)"],
tr[style*="background: #e0f2fe"],
tr[style*="background: rgba(224, 242, 254"] {
  background-color: rgba(198, 246, 213, 0.1) !important; /* 替换为浅绿色背景 */
}

/* 覆盖所有蓝色背景的单元格 */
td[style*="background-color: rgb(187, 222, 251)"],
td[style*="background-color: #bbdefb"],
td[style*="background-color: rgba(187, 222, 251"],
td[style*="background: rgb(187, 222, 251)"],
td[style*="background: #bbdefb"],
td[style*="background: rgba(187, 222, 251"],
td[style*="background-color: rgb(224, 242, 254)"],
td[style*="background-color: #e0f2fe"],
td[style*="background-color: rgba(224, 242, 254"],
td[style*="background: rgb(224, 242, 254)"],
td[style*="background: #e0f2fe"],
td[style*="background: rgba(224, 242, 254"] {
  background-color: transparent !important;
}

/* 覆盖所有蓝色背景的span元素 */
span[style*="background-color: rgb(187, 222, 251)"],
span[style*="background-color: #bbdefb"],
span[style*="background-color: rgba(187, 222, 251"],
span[style*="background: rgb(187, 222, 251)"],
span[style*="background: #bbdefb"],
span[style*="background: rgba(187, 222, 251"],
span[style*="background-color: rgb(224, 242, 254)"],
span[style*="background-color: #e0f2fe"],
span[style*="background-color: rgba(224, 242, 254"],
span[style*="background: rgb(224, 242, 254)"],
span[style*="background: #e0f2fe"],
span[style*="background: rgba(224, 242, 254"] {
  background-color: #c6f6d5 !important; /* 替换为绿色背景 */
  color: #22543d !important;
  border-color: #9ae6b4 !important;
}

/* 特别处理第83行 */
table.coverage tr:nth-child(83) {
  background-color: rgba(198, 246, 213, 0.1) !important; /* 浅绿色背景 */
}

table.coverage tr:nth-child(83) td.text {
  background-color: transparent !important;
}

/* 修复行号、覆盖率指示器和代码内容的对齐问题 */
table.coverage {
  table-layout: fixed !important;
}

table.coverage td.line-count {
  width: 50px !important;
  min-width: 50px !important;
  max-width: 50px !important;
  vertical-align: middle !important;
}

table.coverage td.line-coverage {
  width: 80px !important;
  min-width: 80px !important;
  max-width: 80px !important;
  vertical-align: middle !important;
  text-align: center !important;
}

table.coverage td.text {
  width: auto !important;
  vertical-align: middle !important;
}

/* 强制所有新增语句显示为绿色 */
table.coverage span.cline-new {
  background-color: #c6f6d5 !important;
  color: #22543d !important;
  border-color: #9ae6b4 !important;
}
