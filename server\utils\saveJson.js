const { createDir, writeJsonFile, clearDirectory, mergedJson, getFileNames } = require('./tools');
const fs = require("fs");
// 处理提交过来的json数据
const saveJson = function ({ data, country, applicationName }) {
    return new Promise(async (resolve, reject) => {
        try {
            // 1.创建文件夹
            console.log("----->saveJson 获取文件名")
            const { maxTimestamp } = await getFileNames(`./json/${country}/${applicationName}`)
            let dataPath = `./json/${country}/${applicationName}/${maxTimestamp}`
            console.log("----->saveJson 创建文件夹")
            let dirPath = await createDir(dataPath)
            // 2.文件写入
            const timestamp = new Date().getTime();
            console.log("----->saveJson 开始写入文件")
            await writeJsonFile(`${dirPath}/${timestamp}.json`, data)
            // 3.合并文件
            console.log("----->saveJson 开始合并文件")
            await mergedCoverage({ country, applicationName, dataPath })
            resolve({ code: 1, data: true, msg: '请求成功' })
        } catch (error) {
            resolve({ code: 401, data: false, msg: '请先创建任务' })
        }
    });
}

// 合并json文件
const mergedCoverage = function ({ country, applicationName, dataPath }) {
    return new Promise(async (resolve, reject) => {
        try {
            // 1.合并josn文件
            console.log("----->mergedCoverage 合并josn文件")
            let { coverageMap } = await mergedJson({ dataPath })
            // 2.清空文件夹下面所有文件
            console.log("----->mergedCoverage 清空文件夹下面所有文件")
            await clearDirectory(dataPath)
            // 3.写入合并后的文件
            console.log("----->mergedCoverage 写入合并后的文件")
            fs.writeFileSync(`${dataPath}/merged-coverage.json`, JSON.stringify(coverageMap.toJSON()));
            resolve({ country, applicationName, success: true })
        } catch (error) {
            reject(`${error.message}`)
        }
    });
};

module.exports = {
    saveJson
};
