{"name": "coverage-report-system", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "webpack", "dev:front": "webpack --watch", "dev:server": "nodemon server/index.js", "dev": "concurrently \"npm run dev:front\" \"npm run dev:server\"", "clean": "rimraf public/bundle.js public/bundle.js.map coverage", "serve": "cross-env APP_IP=https://localhost:443 node server/index.js", "pro": "cross-env APP_IP=https://***********:443 nohup node server/index.js > coverage/output.log 2>&1 &", "kill": "pkill -f server/index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"abort-controller": "^3.0.0", "axios": "^1.8.4", "body-parser": "^2.2.0", "compare-json": "^0.4.0", "cors": "^2.8.5", "diff": "^7.0.0", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^4.21.2", "express-ejs-layouts": "^2.5.1", "istanbul-lib-coverage": "^3.2.2", "istanbul-lib-report": "^3.0.1", "istanbul-lib-source-maps": "^5.0.6", "istanbul-reports": "^3.1.7", "nyc": "^17.1.0", "simple-git": "^3.27.0"}, "devDependencies": {"concurrently": "^6.0.0", "cross-env": "^7.0.3", "nodemon": "^3.1.9", "rimraf": "^6.0.1"}}