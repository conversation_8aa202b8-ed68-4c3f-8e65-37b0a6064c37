body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  line-height: 1.6;
}

header {
  background-color: #409eff;
  color: white;
  padding: 1rem;
}

nav ul {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
}

nav li {
  margin-right: 1rem;
}

nav a {
  color: white;
  text-decoration: none;
  font-size: 16px;
  font-weight: bold;
}

nav li.active a {
  color: #33ff92;
}

.container {
  width: 80%;
  max-width: 1200px;
  margin: 2rem auto;
}

.team-list {
  list-style: none;
  padding: 0;
}

.team-list li {
  padding: 0.5rem;
  background-color: #f4f4f4;
  margin-bottom: 0.5rem;
}

form div {
  margin-bottom: 1rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
}

input,
textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
}

button {
  background-color: #409eff;
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  cursor: pointer;
}

button:hover {
  background-color: #555;
}

.alert {
  padding: 1rem;
  margin-bottom: 1rem;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
}
.main {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}
.content {
  flex: 1;
  overflow-y: auto;
}
.footer {
  background-color: #409eff;
  color: #fff;
  height: 57px;
  line-height: 57px;
  text-align: center;
  font-size: 20px;
  font-weight: bold;
}
